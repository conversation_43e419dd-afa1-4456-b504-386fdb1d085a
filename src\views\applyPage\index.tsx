import React from 'react';
import {observer, inject} from 'mobx-react';
import {IMainStore} from '@/store';
import {Layout, toast, Input, Modal, Button, confirm} from 'amis';
import {RouteComponentProps, Switch} from 'react-router';

import AMISRenderer from '@/component/AMISRenderer';
import {configureManagerEditorPlugin} from '@/editor/DisabledEditorPlugin'; // 用于隐藏一些不需要的Editor预置组件
import {showRenderers} from '@/editor/disabledRenderers';
import {MoveClassOrPage} from '@/views/applyPage/component/moveClassOrPage/index';
import dajian from '@/image/common_icons/dajian.png';
import gongzuo from '@/image/common_icons/gongzuo.png';
import {getSystemComponentList} from '@/utils/api/api';

// 页面样式
import './index.scss';
import './nav-item-settings.scss';
// 选择创建页面类型组件
import InitPageType from '@/component/InitPageType/index';
// 页面表单页面组件
import PageContent from '@/component/PageContent/index';
// 页面流程页面组件
import PageProcessContent from '@/component/PageProcessContent/index';
// 页面报表页面组件
import PageReportContent from '@/component/PageReportContent/index';
// 页面链接页面组件
import PagelinkContent from '@/component/PagelinkContent/index';
// wiki页面组件
import PageWikiContent from '@/component/PageWikiContent/index';
// 页面自定义页面组件
import PageCustompage from '@/component/PageCustompage/index';
// 页面增删改查页面组件
import PageCRUDpage from '@/component/PageCRUDpage/index';
// 页面数据源导入页面组件
import PageDataSetImport from '@/component/PageDataSetImport/index';
// 页面amis报表页面大屏页面组件
import PageDashboardContent from '@/component/PageDashboardContent/index';
// 页面云网盘
import PageYunWangPanContent from '@/component/PageYunWangPanContent/index';
// 页面笔记
import PageNotesContent from '@/component/PageNotesContent/index';
// 页面多维度表格页面组件
import PageMultiDimensionalTableContent from '@/component/PageMultiDimensionalTableContent/index';
// 页面白板页面组件
import PageWhiteboardContent from '@/component/PageWhiteboardContent/index';
// 页面芋道网盘
import PageYudaoPanContent from '@/component/PageYudaoPanContent/index';
// 页面数据管理
import PageDataManage from '@/component/PageDataManage/index';
// 门户
import PagePortletContent from '@/component/PagePortletpage/index';
// 应用主页审批组件
import {default as Approval} from '@/views/approval/index';

// 图片引入
import arrow_down_icon from '@/image/common_icons/arrow_down_icon.png';

import check_icon from '@/image/common_icons/check_icon.png';
import type_folder from '@/image/page_icons/type_folder.png';
import type_form from '@/image/page_icons/type_form.png';
import type_process from '@/image/page_icons/type_process.png';
import type_link from '@/image/page_icons/type_link.png';
import type_report from '@/image/page_icons/type_report.png';
import type_custompage from '@/image/page_icons/type_custompage.png';
import type_wiki from '@/image/page_icons/type_wiki.png';
import type_dashboard from '@/image/page_icons/type_dashboard.png';
import type_yunWangPan from '@/image/page_icons/type_yunWangPan.png';
import type_notes from '@/image/page_icons/type_notes.png';
import type_multiDimensionalTable from '@/image/page_icons/type_multiDimensionalTable.png';
import type_whiteboard from '@/image/page_icons/type_whiteboard.png';
import type_crud from '@/image/page_icons/type_crud.png';
import type_dataSetImport from '@/image/page_icons/type_dataSet_import.png';
import type_yudaopan from '@/image/page_icons/type_yudaopan.png';
import type_dataManage from '@/image/page_icons/type_dataManage.png';
import type_portlet from '@/image/page_icons/type_portlet.png';
import type_excelImport from '@/image/page_icons/type_excelImport.png';
// 接口api
import {
  javaApplication, //获取应用数据
  javaApplicationUpdate, //更新应用数据
  getApplicationPageAndClassList, //获取应用页面和分类
  createApplicationPageAndClass, //创建应用页面和分类
  updateApplicationPageAndClass, //更新应用页面和分类
  deleteApplicationPageAndClass, //删除应用页面和分类\取消分类
  createFormData //创建表单数据
} from '@/utils/api/api';


import {
  createReportObj,
  createIframeObj,
  createFromObj
} from '@/utils/schemaPageTemplate/createPageObjs';
import AppPublish from '@/views/AppPublish';
import {LogoutConfirmDialog} from '@/utils/schemaDataSet/LogoutConfirmDialog';
import {EditNoBgEntryIcon} from '@/utils/schemaDataSet/EditNoBgEntryIcon';

import AppSetting from '@/views/applySettings/index';
import FullScreen from '@/views/applySettings/fullScreen/index';
import {marginProp} from 'amis-ui/lib/components/virtual-list/constants';
import DataSet from '@/views/applySettings/component/dataSet/index';
import DataSource from '@/views/applySettings/component/dataSource/index';
import ControlDictionary from '@/views/manage/component/controlDictionary/index';
// 导入菜单管理组件
import MenuManager from '@/views/applyPage/component/menuManager/index';
// 导入页面管理组件
import PageManager from '@/views/applyPage/component/pageManager/index';

import CommonHeader from '@/views/components/CommonHeader';

declare global {
  interface Window {
    storeInstance: IMainStore;
  }
}

export default inject('store')(
  observer(function ({
    store,
    location,
    history,
    match
  }: {store: IMainStore} & RouteComponentProps<{
    appId: string;
    playType: string;
    form?: string;
    appSetMenu?: string;
    fullScreen?: string;
  }>) {
    // 获取应用数据
    const [applyData, setApplyData] = React.useState<any>({});
    // 是否是应用模板
    const [isAppTemplate, setIsAppTemplate] = React.useState<any>(false);

    const baseMenuList = [
      {
        id: 1,
        name: '菜单管理',
        path: `/app${match.params.appId}/admin/menuManager`,
        icon: 'fa fa-bars',
        children: [],
        system: true
      },
      {
        id: 2,
        name: ' 数据管理',
        path: `/app${match.params.appId}/admin/dataManage`,
        icon: 'fa fa-building',
        type: 2,
        children: [
          {
            id: 21,
            name: '控件字典管理',
            path: `/app${match.params.appId}/admin/controlDictionary`,
            icon: 'fas fa-hammer',
            children: [],
            system: true
          },
          {
            id: 22,
            name: '数据源管理',
            path: `/app${match.params.appId}/admin/dataSource`,
            icon: 'fa fa-database',
            children: [],
            system: true
          },
          {
            id: 23,
            name: '数据集管理',
            path: `/app${match.params.appId}/admin/dataSet`,
            icon: 'fa fa-table',
            children: [],
            system: true
          }
        ]
      }
    ];

    // 合并基础菜单和API返回的菜单（在应用模板的情况下）
    const [combinedMenuList, setCombinedMenuList] = React.useState<any[]>([]);

    // 添加一个生成企业 Logo 的函数
    const generateBusinessLogo = (name: string) => {
      if (!name) return '';
      // 获取企业名称的第一个字
      return name.charAt(0).toUpperCase();
    };

    // 引用菜单列表数据（分类+页面）
    const [appMenuList, setAppMenuList] = React.useState<any>([]);

    // 选中访问的页面数据
    const [checkedPageData, setCheckedPageData] = React.useState<any>(false);

    const handleJavaApplication = () => {
      const data = {
        id: Number(match.params.appId)
      };
      javaApplication(data).then(res => {
        if (res.code === 0) {
          setApplyData(res.data);
          // 判断是否是应用模板
          const appTemp = res.data.type == '2';
          setIsAppTemplate(appTemp);
          // if (res.data.projectId == null || res.data.isTemplate == 1) {
          //   setIsAppTemplate(true);
          // } else {
          //   setIsAppTemplate(false);
          // }
          handleGetApplicationPageAndClassList(undefined, appTemp);
          store.setApplyName(res.data.name);
          if (!res.data.logo) {
            res.data.logo = '';
          }
          store.setApplyLogo(res.data.logo);
        } else {
          setApplyData({});
          toast.error(res.msg);
        }
      });
    };

    const handlegetSystemComponentList = () => {
      let data = {
        pageNo: 1,
        pageSize: 999
      };
      getSystemComponentList(data).then((res: any) => {
        if (res.code == 0) {
          // 保存系统组件列表数据
          const componentConfigs = res.data.list || [];
          // 过滤出启用的组件配置
          const enabledConfigs = componentConfigs

          // 在编辑页面时使用这些组件配置
          if (checkedPageData && checkedPageData.pageType) {
            // 根据页面类型选择不同的渲染器
            let rendererType = 'page';
            if (
              checkedPageData.pageType == 1 ||
              checkedPageData.pageType == 2
            ) {
              rendererType = 'form';
            } else if (
              checkedPageData.pageType == 7 ||
              checkedPageData.pageType == 16
            ) {
              rendererType = 'dashboard';
            } else if (checkedPageData.pageType == 12) {
              rendererType = 'crud';
            } else if (checkedPageData.pageType == 17) {
              rendererType = 'portlet';
            }

            // 获取对应类型的渲染器列表
            const renderers = showRenderers(rendererType) || [];

            // 配置编辑器插件
            configureManagerEditorPlugin(renderers, enabledConfigs);
            console.log(
              `已配置编辑器插件，使用 ${rendererType} 类型的渲染器和 ${enabledConfigs.length} 个系统组件`
            );
          }
        } else {
          toast.error(res.msg);
        }
      });
    };
    // 获取应用分类与页面
    const handleGetApplicationPageAndClassList = (
      pageId?: number,
      isAppTemplate?: boolean
    ) => {
      let params = {
        applicationId: Number(match.params.appId),
        applicantOrBackend: 1
      };
      getApplicationPageAndClassList(params).then(res => {
        if (res.code === 0) {
          console.log('res.data', res.data);
          let data = res.data;
          setAppMenuList(data);

          // 如果是应用模板，合并基础菜单和API返回的菜单
          if (isAppTemplate) {
            const newCombinedMenuList = [...baseMenuList, ...data];
            setCombinedMenuList(newCombinedMenuList);
            console.log('combinedMenuList', newCombinedMenuList);
          }

          if (pageId) {
            // 在应用模板模式下，需要同时在combinedMenuList和appMenuList中查找页面
            if (isAppTemplate) {
              let arr = [];
              // 在combinedMenuList中查找
              arr = findPageInMenuList(pageId, [...baseMenuList, ...data]);
              if (arr.length > 0) {
                handleCheckedPageData(arr[0]);
              }
            } else {
              let arr = data.filter((item: any) => {
                return item.id == pageId;
              });
              if (arr.length > 0) {
                handleCheckedPageData(arr[0]);
              }
            }
          }
        } else {
          setAppMenuList([]);
          if (isAppTemplate) {
            setCombinedMenuList([...baseMenuList]);
          }
        }
      });
    };

    // 辅助函数：在菜单列表中递归查找指定ID的页面
    const findPageInMenuList = (pageId: number, menuList: any[]): any[] => {
      let result: any[] = [];

      // 遍历菜单列表
      for (const item of menuList) {
        // 检查当前项
        if (item.id == pageId) {
          result.push(item);
          break;
        }

        // 递归检查子菜单
        if (item.children && item.children.length > 0) {
          const found = findPageInMenuList(pageId, item.children);
          if (found.length > 0) {
            result = found;
            break;
          }
        }
      }

      return result;
    };

    // 转换菜单列表为导航链接格式
    const transformMenuListToNavLinks = (menuList: any[]): any[] => {
      console.log('transformMenuListToNavLinks 开始处理菜单数据:', menuList);

      return menuList.map((item: any) => {
        const navItem: any = {
          key: item.id?.toString(),
          label: item.name,
          icon: item.icon,
          pageType: item.pageType,
          type: item.type,
          id: item.id,
          parentId: item.parentId,
          // 保留原始数据用于后续处理
          ...item
        };

        // 为分类项目添加虚拟的to属性，使其可选择
        if (item.type === 2) {
          navItem.to = `#category-${item.id}`;
          console.log('🔄 为分类添加虚拟to属性:', navItem);
        }

        // 如果有子菜单，递归转换
        if (item.children && item.children.length > 0) {
          navItem.children = transformMenuListToNavLinks(item.children);
        }

        return navItem;
      });
    };

    // 临时的页面类型查找函数
    const findPageTypeByField = (fieldName: string, value: any) => {
      // 简单返回一个默认对象，避免类型错误
      return { imgSrc: '' };
    };

    // 渲染导航项内容
    const renderNavItemContent = (item: any) => {
      const pageTypeInfo = findPageTypeByField('typeNumber', item.pageType);

      return {
        type: 'flex',
        className: 'nav-item-page',
        style: {
          display: 'flex',
          alignItems: 'center',
          width: '100%',
          cursor: 'pointer'
        },
        body: {
          type: 'flex',
          className: 'nav-item-content',
          items: [
          {
            type: 'container',
            className: 'nav-item-icon',
            body: checkedPageData?.id == item.id ? {
              type: 'icon',
              icon: 'fa-solid fa-check',
              className: 'nav-item-check-icon'
            } : null
          },
          {
            type: 'container',
            className: 'nav-item-type-icon',
            body: item.icon && (item.icon.includes('<svg') || item.icon.includes('<span') || item.icon.includes('&lt;span')) ? (() => {
              console.log('使用tpl渲染图标:', item.name, item.icon);
              // 处理可能的HTML转义
              let iconHtml = item.icon;
              if (iconHtml.includes('&lt;') || iconHtml.includes('&gt;') || iconHtml.includes('&quot;')) {
                iconHtml = iconHtml
                  .replace(/&lt;/g, '<')
                  .replace(/&gt;/g, '>')
                  .replace(/&quot;/g, '"')
                  .replace(/&amp;/g, '&');
                console.log('转义后的图标HTML:', iconHtml);
              }
              return {
                type: 'tpl',
                tpl: iconHtml,
                className: 'nav-item-type-icon-svg'
              };
            })() : {
              type: 'image',
              src: pageTypeInfo?.imgSrc || '',
              className: 'nav-item-type-icon-img'
            }
          },
          {
            type: 'container',
            className: 'nav-item-name',
            body: {
              type: 'tpl',
              tpl: item.name || item.label,
              className: 'nav-item-name-text'
            }
          }
        ]
        }
      };
    };

    // 转换合并菜单列表为导航链接格式（应用模板）
    const transformCombinedMenuToNavLinks = (menuList: any[]): any[] => {
      return menuList.map((item: any) => {
        // 添加调试信息
        if (item.icon && item.icon.includes('<span')) {
          console.log('模板菜单发现包含span的图标:', item.name, item.icon);
        }

        const navItem: any = {
          key: item.id?.toString(),
          label: item.name,
          icon: item.icon,
          type: item.type,
          id: item.id,
          path: item.path,
          system: item.system,
          pageType: item.pageType,
          // 保留原始数据
          ...item
        };

        // 如果有子菜单，递归转换
        if (item.children && item.children.length > 0) {
          navItem.children = transformCombinedMenuToNavLinks(item.children);
        }

        return navItem;
      });
    };

    // 获取当前激活的菜单键值
    const getActiveMenuKey = (): string => {
      if (checkedPageData?.id) {
        return checkedPageData.id.toString();
      }

      // 检查系统菜单
      if (match.params.form) {
        const systemMenus = ['menuManager', 'controlDictionary', 'dataSource', 'dataSet'];
        for (const menu of systemMenus) {
          if (match.params.form.includes(menu)) {
            const foundItem = combinedMenuList.find(item =>
              item.path && item.path.includes(menu)
            );
            if (foundItem) {
              return foundItem.id.toString();
            }
          }
        }
      }

      return '';
    };

    // 渲染模板导航项内容
    const renderTemplateNavItemContent = (item: any) => {
      const hasChildren = item.type == 2 || (item.children && item.children.length > 0);
      const isSelected = getActiveMenuKey() === item.id?.toString();

      return {
        type: 'flex',
        className: `nav-item-template ${hasChildren ? 'nav-item-category' : 'nav-item-page'}`,
        items: [
          {
            type: 'container',
            className: hasChildren ? 'nav-item-expand-icon' : 'nav-item-icon',
            body: hasChildren ? {
              type: 'icon',
              icon: `fa-solid fa-caret-${openTypeIds.includes(item.id) ? 'down' : 'right'}`,
              className: 'nav-item-expand-icon-val'
            } : (isSelected ? {
              type: 'icon',
              icon: 'fa-solid fa-check',
              className: 'nav-item-check-icon'
            } : null)
          },
          {
            type: 'container',
            className: 'nav-item-type-icon',
            body: item.icon && (item.icon.includes('<svg') || item.icon.includes('<span') || item.icon.includes('&lt;span')) ? (() => {
              console.log('模板菜单使用tpl渲染图标:', item.name, item.icon);
              // 处理可能的HTML转义
              let iconHtml = item.icon;
              if (iconHtml.includes('&lt;') || iconHtml.includes('&gt;') || iconHtml.includes('&quot;')) {
                iconHtml = iconHtml
                  .replace(/&lt;/g, '<')
                  .replace(/&gt;/g, '>')
                  .replace(/&quot;/g, '"')
                  .replace(/&amp;/g, '&');
                console.log('模板菜单转义后的图标HTML:', iconHtml);
              }
              return {
                type: 'tpl',
                tpl: iconHtml,
                className: 'nav-item-type-icon-svg'
              };
            })() : {
              type: 'icon',
              icon: item.icon || (hasChildren ? 'fa fa-folder' : 'fa fa-file'),
              className: 'nav-item-type-icon-val'
            }
          },
          {
            type: 'tpl',
            tpl: item.name || item.label,
            className: 'nav-item-name-text'
          }
        ]
      };
    };

    // 处理模板菜单项点击
    const handleTemplateMenuItemClick = (item: any) => {
      const hasChildren = item.type == 2 || (item.children && item.children.length > 0);

      if (hasChildren) {
        // 如果有子菜单，切换展开/折叠状态
        handleSidebarTypeSwitch(item.id);
      } else {
        // 如果是普通页面项（type为1且有pageType），则调用handleCheckedPageData
        if (item.type === 1 && (item.pageType || !item.system)) {
          handleCheckedPageData(item);
          return;
        }

        // 如果没有子菜单，处理跳转逻辑
        if (item.path) {
          // 清除选中的页面数据，避免影响系统菜单的跳转
          setCheckedPageData(item);

          // 特殊处理一些系统菜单
          if (item.name === '控件字典管理') {
            history.push(
              `/app${match.params.appId}/${match.params.playType}/controlDictionary`
            );
          } else if (item.name === '数据源管理') {
            history.push(
              `/app${match.params.appId}/${match.params.playType}/dataSource`
            );
          } else if (item.name === '数据集管理') {
            history.push(
              `/app${match.params.appId}/${match.params.playType}/dataSet`
            );
          } else if (item.name === '菜单管理') {
            history.push(
              `/app${match.params.appId}/${match.params.playType}/menuManager`
            );
          } else {
            history.push(item.path);
          }
        }
      }
    };

    React.useEffect(() => {
      window.storeInstance = store;
      // 将拖拽排序函数暴露到全局
      (window as any).handlePageDragSort = handleDragSort;

      // 添加DOM拖拽事件监听器来捕获拖拽目标信息
      const handleDragOver = (e: DragEvent) => {
        const target = e.target as HTMLElement;
        const navItem = target.closest('[data-key]');
        if (navItem) {
          const itemKey = navItem.getAttribute('data-key');
          const itemData = appMenuList.find((item: any) => item.id?.toString() === itemKey);
          if (itemData) {
            (window as any).currentDragTarget = itemData;
            console.log('🔄 拖拽悬停目标:', itemData);
          }
        }
      };

      const handleDrop = (e: DragEvent) => {
        const target = e.target as HTMLElement;
        const navItem = target.closest('[data-key]');
        if (navItem) {
          const itemKey = navItem.getAttribute('data-key');
          const itemData = appMenuList.find((item: any) => item.id?.toString() === itemKey);
          if (itemData && itemData.type === 2) {
            (window as any).dropTarget = itemData;
            console.log('🔄 拖拽放置目标（分类）:', itemData);
          }
        }
      };

      // 添加事件监听器
      document.addEventListener('dragover', handleDragOver);
      document.addEventListener('drop', handleDrop);

      // 清理函数
      return () => {
        document.removeEventListener('dragover', handleDragOver);
        document.removeEventListener('drop', handleDrop);
      };
    }, [store, appMenuList]);

    // 缓存原始菜单顺序，用于拖拽排序优化
    React.useEffect(() => {
      if (appMenuList && appMenuList.length > 0) {
        (window as any).originalMenuOrder = [...appMenuList];
        console.log('🔄 缓存原始菜单顺序:', appMenuList);
      }
    }, [appMenuList]);

    // 监听页面弹窗事件
    React.useEffect(() => {
      const handleShowPageDialog = (event: any) => {
        const { dialogType, pageType } = event.detail;
        console.log('接收到弹窗事件:', dialogType, pageType);

        // 跳转到页面类型选择界面
        setIsInitPageType(true);
        setCheckedPageData(false);
        history.push(`/app${match.params.appId}/${match.params.playType}`);

        // 延迟触发特定页面类型的弹窗
        setTimeout(() => {
          const triggerEvent = new CustomEvent('triggerPageTypeDialog', {
            detail: {
              dialogType,
              pageType
            }
          });
          window.dispatchEvent(triggerEvent);
        }, 100); // 等待 InitPageType 组件渲染完成
      };

      // 添加事件监听器
      window.addEventListener('showPageDialog', handleShowPageDialog);

      // 清理函数
      return () => {
        window.removeEventListener('showPageDialog', handleShowPageDialog);
      };
    }, []);

    // 添加监听 refreshMenuManager 事件，用于在菜单管理操作后刷新菜单列表
    React.useEffect(() => {
      const handleRefreshMenuManager = () => {
        console.log('接收到 refreshMenuManager 事件，刷新菜单列表');
        const isTemplate = applyData.projectId == null;
        handleGetApplicationPageAndClassList(undefined, isTemplate);
      };

      // 添加拖拽排序后刷新页面列表的事件监听器
      const handleRefreshPageList = () => {
        console.log('🔄 接收到刷新页面列表事件');
        handleGetApplicationPageAndClassList();
      };

      window.addEventListener('refreshMenuManager', handleRefreshMenuManager);
      window.addEventListener('refreshPageList', handleRefreshPageList);

      // 组件卸载时移除事件监听
      return () => {
        window.removeEventListener(
          'refreshMenuManager',
          handleRefreshMenuManager
        );
        window.removeEventListener('refreshPageList', handleRefreshPageList);
      };
    }, [applyData]);

    React.useEffect(() => {
      if (appMenuList.length > 0 && !isAppTemplate) {
        if (match.params.form) {
          // 根据得到的页面id，获取初始选中的页面，显示对应内容
          let id = match.params.form.split('page')[1];
          if (id) {
            // 查找匹配的页面
            let foundPage = null;

            // 先在顶层查找
            let arr = appMenuList.filter((item: any) => {
              return item.type == 1 && item.id == id;
            });

            if (arr.length > 0) {
              foundPage = arr[0];
            } else {
              // 在子项中查找
              for (let item of appMenuList) {
                if (item.type != 1 && item.children) {
                  let childArr = item.children.filter((child: any) => {
                    return child.id == id;
                  });

                  if (childArr.length > 0) {
                    foundPage = childArr[0];
                    break;
                  }
                }
              }
            }

            if (foundPage) {
              // 强制更新页面数据，确保页面能够正确刷新
              setCheckedPageData(foundPage);
              setIsInitPageType(false);
            } else {
              history.push(
                `/app${match.params.appId}/${match.params.playType}`
              );
            }
          }
        } else {
          if (match.params.playType == 'workbench') {
            if (appMenuList[0].type == 1) {
              history.push(
                `/app${match.params.appId}/${match.params.playType}/page${appMenuList[0].id}`
              );
            } else {
              //崩溃修复
              if (
                appMenuList.length > 0 &&
                appMenuList[0].children &&
                appMenuList[0].children.length > 0
              ) {
                history.push(
                  `/app${match.params.appId}/${match.params.playType}/page${appMenuList[0].children[0].id}`
                );
              } else {
              }
            }
          }
        }
      }
    }, [appMenuList, match.params.form, isAppTemplate, checkedPageData?.id]);

    // 专门监听URL变化，确保路由切换时能立即响应
    React.useEffect(() => {
      // 当URL变化时，强制刷新页面数据
      if (match.params.form) {
        let id = match.params.form.split('page')[1];
        if (id) {
          // 查找匹配的页面
          let foundPage = null;

          // 在应用模板模式下，在combinedMenuList中查找
          if (isAppTemplate && combinedMenuList.length > 0) {
            foundPage = findPageInMenuList(Number(id), combinedMenuList)[0];
          } else if (appMenuList.length > 0) {
            // 在普通模式下，在appMenuList中查找
            // 先在顶层查找
            let arr = appMenuList.filter((item: any) => {
              return item.type == 1 && item.id == id;
            });

            if (arr.length > 0) {
              foundPage = arr[0];
            } else {
              // 在子项中查找
              for (let item of appMenuList) {
                if (item.type != 1 && item.children) {
                  let childArr = item.children.filter((child: any) => {
                    return child.id == id;
                  });

                  if (childArr.length > 0) {
                    foundPage = childArr[0];
                    break;
                  }
                }
              }
            }
          }

          if (foundPage) {
            // 强制更新页面数据，确保页面能够正确刷新
            setCheckedPageData(foundPage);
            setIsInitPageType(false);
          }
        }
      }
    }, [location.pathname, appMenuList, isAppTemplate, combinedMenuList]); // 添加依赖项确保菜单列表更新时也能响应

    // 添加history监听器，确保所有路由变化都能被捕获
    React.useEffect(() => {
      const unlisten = history.listen((location, action) => {
        // 当路由变化时，检查是否需要更新页面数据
        if (action === 'PUSH' || action === 'REPLACE') {
          const pathname = location.pathname;
          const pageMatch = pathname.match(/\/page(\d+)$/);

          if (pageMatch) {
            const pageId = pageMatch[1];
            let foundPage = null;

            // 在应用模板模式下，在combinedMenuList中查找
            if (isAppTemplate && combinedMenuList.length > 0) {
              foundPage = findPageInMenuList(
                Number(pageId),
                combinedMenuList
              )[0];
            } else if (appMenuList.length > 0) {
              // 在普通模式下，在appMenuList中查找
              // 先在顶层查找
              let arr = appMenuList.filter((item: any) => {
                return item.type == 1 && item.id == pageId;
              });

              if (arr.length > 0) {
                foundPage = arr[0];
              } else {
                // 在子项中查找
                for (let item of appMenuList) {
                  if (item.type != 1 && item.children) {
                    let childArr = item.children.filter((child: any) => {
                      return child.id == pageId;
                    });

                    if (childArr.length > 0) {
                      foundPage = childArr[0];
                      break;
                    }
                  }
                }
              }
            }

            if (foundPage) {
              // 强制更新页面数据，确保页面能够正确刷新
              setCheckedPageData(foundPage);
              setIsInitPageType(false);
            }
          }
        }
      });

      // 清理监听器
      return () => {
        unlisten();
      };
    }, [history, appMenuList, isAppTemplate, combinedMenuList]);

    React.useEffect(() => {
      if (match.params.appId) {
        handleJavaApplication();
        handlegetSystemComponentList();
        // handleGetApplicationPageAndClassList();
      }
    }, [match.params.appId]);

    // 处理baseMenuList的初始展开状态
    React.useEffect(() => {
      if (isAppTemplate) {
        // 默认展开数据管理菜单
        const dataManageMenu = baseMenuList.find(item => item.type === 2);
        if (dataManageMenu) {
          setOpenTypeIds([dataManageMenu.id]);
        }

        // 确保在应用模板模式下初始化合并菜单
        if (appMenuList.length > 0) {
          // 如果API已经返回了菜单数据，合并它们
          setCombinedMenuList([...baseMenuList, ...appMenuList]);
        } else {
          // 否则只使用基础菜单
          setCombinedMenuList([...baseMenuList]);
        }

        // 如果有路由参数form，则找到对应的菜单项并选中
        if (match.params.form) {
          // 在combinedMenuList中查找匹配的菜单项
          let foundMenuItem = findMenuItemByPath(
            combinedMenuList,
            match.params.form
          );
          if (foundMenuItem) {
            // 如果找到了菜单项，设置为选中状态
            setCheckedPageData(foundMenuItem);

            // 展开其所有父级菜单
            if (foundMenuItem.parentId) {
              // 查找父级菜单并添加到openTypeIds中
              const parentIds = getParentMenuIds(
                combinedMenuList,
                foundMenuItem.parentId
              );
              setOpenTypeIds(prev => [...new Set([...prev, ...parentIds])]);
            }
          }
        }
      }
    }, [isAppTemplate, match.params.form, appMenuList]);

    // 查找菜单项的函数
    const findMenuItemByPath = (menuList: any[], path: string): any => {
      for (const item of menuList) {
        // 检查当前菜单项
        if (item.path && item.path.includes(path)) {
          return item;
        }

        // 递归检查子菜单
        if (item.children && item.children.length > 0) {
          const found = findMenuItemByPath(item.children, path);
          if (found) return found;
        }
      }
      return null;
    };

    // 获取所有父级菜单ID的函数
    const getParentMenuIds = (
      menuList: any[],
      parentId: number | string
    ): (number | string)[] => {
      const result: (number | string)[] = [parentId];

      for (const item of menuList) {
        if (item.id === parentId) {
          // 如果找到了直接父级，检查它是否也有父级
          if (item.parentId) {
            result.push(...getParentMenuIds(menuList, item.parentId));
          }
          return result;
        }

        // 递归检查子菜单
        if (item.children && item.children.length > 0) {
          for (const child of item.children) {
            if (child.id === parentId) {
              // 找到了直接父级，将当前菜单项的ID添加到结果中
              result.push(item.id);
              // 继续向上查找
              if (item.parentId) {
                result.push(...getParentMenuIds(menuList, item.parentId));
              }
              return result;
            }
          }

          // 在当前菜单项的子菜单中递归查找
          const parentIds = getParentMenuIds(item.children, parentId);
          if (parentIds.length > 0) {
            result.push(item.id);
            if (item.parentId) {
              result.push(...getParentMenuIds(menuList, item.parentId));
            }
            return [...new Set([...result, ...parentIds])];
          }
        }
      }

      return result;
    };

    // 选中访问的页面或者分类数据
    const handleCheckedPageData = (item: any) => {
      // 如果是系统菜单项（baseMenuList中的菜单），特殊处理
      if (isAppTemplate && item.system) {
        setCheckedPageData(item);
        // 对于系统菜单，直接使用path进行导航
        if (item.path) {
          history.push(item.path);
        }
        return;
      }

      // 强制更新页面数据，确保页面能够正确刷新
      handleEdit(item.pageType);
      setCheckedPageData(item);
      //路由
      history.push(
        `/app${match.params.appId}/${match.params.playType}/page${item.id}`
      );
    };

    const handleEdit = (pageType: number): void => {
      // 根据页面类型选择不同的渲染器
      let rendererType = 'page';
      if (pageType == 1 || pageType == 2) {
        rendererType = 'form';
      } else if (pageType == 7 || pageType == 16) {
        rendererType = 'dashboard';
      } else if (pageType == 12) {
        rendererType = 'crud';
      } else if (pageType == 17) {
        rendererType = 'portlet';
      } else {
        rendererType = 'page';
      }
      console.log('rendererType', rendererType);
      let renderers: any = showRenderers(rendererType);

      // 重新获取系统组件列表并配置编辑器
      let data = {
        pageNo: 1,
        pageSize: 999
      };
      getSystemComponentList(data)
        .then((res: any) => {
          if (res.code == 0) {
            // 保存系统组件列表数据
            const componentConfigs = res.data.list || [];
            // 过滤出启用的组件配置
            const enabledConfigs = componentConfigs

            // 配置编辑器插件
            configureManagerEditorPlugin(renderers, enabledConfigs);
            console.log(
              `已配置编辑器插件，使用 ${rendererType} 类型的渲染器和 ${enabledConfigs.length} 个系统组件`
            );
          } else {
            // 如果获取失败，仍然使用渲染器配置编辑器，但不使用组件配置
            configureManagerEditorPlugin(renderers);
            toast.error(res.msg);
          }
        })
        .catch(() => {
          // 出错时降级处理：只配置渲染器，不使用组件配置
          configureManagerEditorPlugin(renderers);
        });
    };

    /* data 数据 */
    // 固定操作菜单
    const fixedPlayMenu = {
      pending: {
        name: '待我处理',
        path: `${
          process.env.NODE_ENV === 'development'
            ? 'http://localhost:88/#/workspace/unfinished'
            : 'https://javaddm.fjpipixia.com/wflow/#/workspace/unfinished'
        }`
      },
      transactors: {
        name: '我已处理',
        path: `${
          process.env.NODE_ENV === 'development'
            ? 'http://localhost:88/#/workspace/finished'
            : 'https://javaddm.fjpipixia.com/wflow/#/workspace/finished'
        }`
      },
      create: {
        name: '我创建的',
        path: `${
          process.env.NODE_ENV === 'development'
            ? 'http://localhost:88/#/workspace/submit'
            : 'https://javaddm.fjpipixia.com/wflow/#/workspace/submit'
        }`
      },
      ccusers: {
        name: '抄送我的',
        path: `${
          process.env.NODE_ENV === 'development'
            ? 'http://localhost:88/#/workspace/cc'
            : 'https://javaddm.fjpipixia.com/wflow/#/workspace/cc'
        }`
      }
    };

    // 编辑应用名称的item
    const [editApplyName, setEditApplyName] = React.useState<any>(false);
    // 是否显示编辑应用名称的input
    const [openApplyNameInput, setOpenApplyNameInput] =
      React.useState<any>(false);

    // 展开分类，数组保存分类的id
    const [openTypeIds, setOpenTypeIds] = React.useState<any[]>([]);

    //创建页面切换到选择页面类型页面
    const [isInitPageType, setIsInitPageType] = React.useState<any>(false);
    // 折叠页面导航菜单
    const [isCollapsePage, setIsCollapsePage] = React.useState<any>(false);

    // 页面搜索状态
    const [searchKeyword, setSearchKeyword] = React.useState<string>('');
    const [filteredMenuList, setFilteredMenuList] = React.useState<any[]>([]);
    const [searchExpanded, setSearchExpanded] = React.useState<boolean>(false);

    // 搜索过滤逻辑
    React.useEffect(() => {
      if (!searchKeyword.trim()) {
        setFilteredMenuList(appMenuList);
      } else {
        const filterMenuItems = (items: any[]): any[] => {
          const filtered: any[] = [];

          items.forEach(item => {
            // 检查当前项是否匹配搜索关键词
            const matchesKeyword = item.name?.toLowerCase().includes(searchKeyword.toLowerCase());

            if (item.children && item.children.length > 0) {
              // 递归过滤子项
              const filteredChildren = filterMenuItems(item.children);

              if (matchesKeyword || filteredChildren.length > 0) {
                // 如果当前项匹配或有匹配的子项，则包含此项
                filtered.push({
                  ...item,
                  children: filteredChildren
                });
              }
            } else if (matchesKeyword) {
              // 叶子节点且匹配关键词
              filtered.push(item);
            }
          });

          return filtered;
        };

        setFilteredMenuList(filterMenuItems(appMenuList));
      }
    }, [searchKeyword, appMenuList]);

    // 重命名页面名称/页面分类的item
    const [renamePageOrClassItem, setRenamePageOrClassItem] =
      React.useState<any>(false);

    // 重命名弹框状态
    const [showRenameDialog, setShowRenameDialog] = React.useState<boolean>(false);
    // 重命名弹框中的新名称
    const [newItemName, setNewItemName] = React.useState<string>('');

    // 是否打开右键弹出层删除菜单
    const [rightPageMenu, setRighPagetMenu] = React.useState<any>(false);
    // 右键编辑页面菜单的数据item
    const [rightMenuItem, setRightMenuItem] = React.useState<any>(false);
    // 右键编辑菜单的类型: 1-页面 2-分类
    const [rightTypeMenu, setRightTypeMenu] = React.useState<any>(1);
    // 右键编辑页面菜单位置
    const [rightPageMenuPosition, setRightPageMenuPosition] =
      React.useState<any>({
        left: 0,
        top: 0
      });

    const [mianMenuBar, setMianMenuBar] = React.useState<any>(false);

    // 添加状态控制
    const [showLogoutDialog, setShowLogoutDialog] = React.useState(false);

    // 图标编辑相关状态
    const [showEditIconDialog, setShowEditIconDialog] = React.useState(false);
    const [editIconItem, setEditIconItem] = React.useState<any>(null);

    /* data 数据 end */

    /* methods 方法 */

    // 解析应用图标内容，提取SVG、颜色和背景色
    const parseAppIcon = (iconData: any) => {
      console.log('🔍 parseAppIcon 解析图标数据:', {
        iconData,
        type: typeof iconData,
        isObject: typeof iconData === 'object',
        hasSpan: typeof iconData === 'string' && iconData.includes('<span')
      });

      if (!iconData) {
        return {
          icon: '',
          iconColor: '#000000',
          iconBg: '#F5F7FA'
        };
      }

      // 如果是对象格式的图标
      if (typeof iconData === 'object' && iconData.svg) {
        console.log('解析对象格式图标:', iconData.svg);
        return {
          icon: iconData.svg,
          iconColor: '#000000',
          iconBg: '#F5F7FA'
        };
      }

      // 如果是字符串格式的HTML
      if (typeof iconData === 'string') {
        // 如果是完整的span元素，需要解析其中的内容
        if (iconData.includes('<span')) {
          console.log('解析带样式的HTML图标');

          // 提取style属性中的颜色和背景色
          const colorMatch = iconData.match(/color:\s*([^;,]+)/);
          const bgMatch = iconData.match(/background:\s*([^;,]+)/);

          // 提取SVG内容或图标类名
          const svgMatch = iconData.match(/<svg[^>]*>.*?<\/svg>/s);
          const iconMatch = iconData.match(/<i class="([^"]+)"/);

          const result = {
            icon: svgMatch ? svgMatch[0] : (iconMatch ? iconMatch[1] : ''),
            iconColor: colorMatch ? colorMatch[1].trim() : '#000000',
            iconBg: bgMatch ? bgMatch[1].trim() : '#F5F7FA'
          };

          console.log('解析结果:', result);
          return result;
        }

        // 如果是纯SVG，直接返回
        console.log('解析纯SVG图标');
        return {
          icon: iconData,
          iconColor: '#000000',
          iconBg: '#F5F7FA'
        };
      }

      // 默认返回
      return {
        icon: '',
        iconColor: '#000000',
        iconBg: '#F5F7FA'
      };
    };

    // 处理图标保存
    const handleIconSave = (values: any) => {
      console.log('处理页面图标保存:', values);

      if (!values || !Array.isArray(values) || values.length === 0) {
        console.log('无效的数据格式，取消更新');
        return;
      }

      const iconData = values[0];
      if (!iconData) {
        console.log('无图标数据，取消更新');
        return;
      }

      if (!editIconItem) {
        console.log('没有选中的页面项，取消更新');
        return;
      }

      // 获取当前的图标数据作为默认值
      const currentIconData = parseAppIcon(editIconItem?.icon || '');

      // 清理图标数据，移除可能的引号
      let cleanIcon = iconData.icon || '';
      if (typeof cleanIcon === 'string') {
        // 移除可能的外层引号
        cleanIcon = cleanIcon.replace(/^['"]|['"]$/g, '').trim();
      }

      // 使用新的图标数据，但保留原有的颜色和背景色（如果用户没有修改）
      const finalIconColor = iconData.iconColor || currentIconData.iconColor || '#000000';
      const finalIconBg = iconData.iconBg || currentIconData.iconBg || '#F5F7FA';

      // 构建新的图标HTML，包含样式
      const newIconHtml = `<span style="color:${finalIconColor};background:${finalIconBg};display:inline-flex;align-items:center;justify-content:center;border-radius:8px;font-size:32px;vertical-align:middle">${cleanIcon}</span>`;

      // 更新页面数据
      const updatedItem = {
        ...editIconItem,
        icon: newIconHtml  // 保存完整的带样式HTML
      };

      console.log('准备更新页面图标:', updatedItem);

      // 调用更新API
      updateApplicationPageAndClass(updatedItem).then(res => {
        console.log('图标更新API响应:', res);
        if (res.code === 0) {
          // 刷新页面列表
          const isTemplate = applyData.projectId == null || applyData.isTemplate == 1;
          handleGetApplicationPageAndClassList(undefined, isTemplate);
          toast.success('图标设置成功');
        } else {
          toast.error(res.msg || '图标设置失败');
        }
      }).catch(error => {
        console.error('图标更新失败:', error);
        toast.error('图标设置失败');
      });

      // 关闭图标编辑对话框
      setShowEditIconDialog(false);
      setEditIconItem(null);
    };

    // 双击编辑应用名称
    const openEditApplyName = (e: any, name: any) => {
      if (e.detail === 2 && match.params.playType == 'admin') {
        console.log('双击编辑应用名称', name);
        setEditApplyName(name);
        setOpenApplyNameInput(true);
      }
    };
    // 更新保存应用名称
    const saveEditApplyName = () => {
      if (
        editApplyName.trim().length === 0 ||
        editApplyName.trim() === applyData.name
      ) {
        setEditApplyName(false);
        setOpenApplyNameInput(false);
        return;
      }
      let data = applyData;
      data.name = editApplyName;
      javaApplicationUpdate(data).then((res: any) => {
        if (res.code === 0) {
          handleJavaApplication();
          setEditApplyName(false);
          setOpenApplyNameInput(false);
          toast.success('修改成功');
        } else {
          toast.error(res.msg);
        }
      });
    };

    // 展开、收起分类事件
    const handleSidebarTypeSwitch = (id: string) => {
      if (openTypeIds.includes(id)) {
        setOpenTypeIds(openTypeIds.filter((item: string) => item !== id));
      } else {
        setOpenTypeIds([...openTypeIds, id]);
      }
    };

    // 创建新分类 - 检查该应用内创建新分类的名称是否重复
    const checkNewPagesTypeName = (name: string) => {
      let res: any = false;
      for (let i = 0; appMenuList.length > i; i++) {
        if (
          !appMenuList[i].page_class_id &&
          appMenuList[i].page_class_id !== ''
        ) {
          if (appMenuList[i].name === name) {
            res = true;
          }
        }
      }
      return res;
    };
    // 创建新分类
    const handleNewPagesType = (index: number, categoryInfo?: any) => {
      // if (checkedPageData.parentId) {
      //   toast.error('已有分类存在');
      //   return;
      // }
      let name = '';
      if (checkNewPagesTypeName(`分类 ${index}`)) {
        index++;
        handleNewPagesType(index, categoryInfo);
        return;
      } else {
        name = `分类 ${index}`;
      }
      let data: any = {
        applicationId: Number(match.params.appId), //应用id
        applicantOrBackend: 1,
        parentId: 0, //父级id
        type: 2, //类型
        name, //类名
        // 添加图标信息 - 构建带颜色的HTML格式
        icon: categoryInfo?.icon && categoryInfo?.color ?
          `<span style="color:${categoryInfo.color};display:inline-flex;align-items:center;justify-content:center;"><i class="${categoryInfo.icon}"></i></span>` :
          (categoryInfo?.icon || ''),
        iconColor: categoryInfo?.color || ''
      };
      createApplicationPageAndClass(data)
        .then((res: any) => {
          if (res.code === 0) {
            if (checkedPageData) {
              // 选中了页面的情况下
              handlePageMoveClass(res.data.id);
              // 且展开
              setOpenTypeIds([...openTypeIds, res.data.id]);
            } else {
              // 更新侧边菜单路由列表
              handleGetApplicationPageAndClassList();
              toast.success('创建成功');
            }
          } else {
            toast.error('创建失败:', res.msg);
          }
        })
        .catch((err: any) => {
          toast.error('创建失败 Err：', err);
        });
    };

    // 创建新页面
    const onSaveSuccess = (pageId: any) => {
      setIsInitPageType(false);
      handleGetApplicationPageAndClassList(pageId);
    };

    // 处理从MyNavPage传来的页面创建请求
    const handleCreatePageFromNav = (pageType: any) => {
      console.log('从导航创建页面:', pageType);

      // 根据页面类型执行相应的创建逻辑
      switch (pageType.typeName) {
        case 'form':
        case 'process':
        case 'excelImport':
          // 直接创建页面
          handleCreatePageDirect(pageType);
          break;
        case 'dashboard':
        case 'portlet':
        case 'custompage':
        case 'yudaopan':
        case 'dataSetImport':
        case 'link':
          // 这些类型也直接创建页面，带上图标信息
          handleCreatePageDirect(pageType);
          break;
        default:
          // 未知类型跳转到页面类型选择界面
          setIsInitPageType(true);
          setCheckedPageData(false);
          history.push(`/app${match.params.appId}/${match.params.playType}`);
      }
    };

    // 直接创建页面（用于表单类型）
    const handleCreatePageDirect = (pageType: any) => {
      const getTotalPages = () => {
        let res = 1;
        if (appMenuList) {
          for (let i = 0; appMenuList.length > i; i++) {
            if (appMenuList[i].children) {
              res += appMenuList[i].children.length;
            } else {
              res++;
            }
          }
        }
        return res;
      };

      const pageName = `页面 ${getTotalPages()}`;

      // 确定parentId：如果当前选中的是分类，则将新页面归属到该分类下
      let parentId = 0;
      if (checkedPageData && checkedPageData.type === 2) {
        // 当前选中的是分类
        parentId = checkedPageData.id;
        console.log(`🔄 新增页面 - 将页面归属到选中的分类 ${checkedPageData.name}(${parentId}) 下`);
      } else if (checkedPageData && checkedPageData.parentId) {
        // 当前选中的是页面，将新页面归属到同一个分类下
        parentId = checkedPageData.parentId;
        console.log(`🔄 新增页面 - 将页面归属到与选中页面相同的分类 ${parentId} 下`);
      } else {
        console.log('🔄 新增页面 - 将页面归属到根级别');
      }

      let data: any = {
        url: '',
        applicationId: match.params.appId,
        applicantOrBackend: 1,
        pageType: pageType.typeNumber,
        parentId: parentId,
        name: pageName,
        type: 1,
        // 添加图标信息 - 构建带颜色的HTML格式
        icon: pageType.icon && pageType.iconColor ?
          `<span style="color:${pageType.iconColor};display:inline-flex;align-items:center;justify-content:center;"><i class="${pageType.icon}"></i></span>` :
          (pageType.icon || ''),
        iconColor: pageType.iconColor || ''
      };

      // 调用创建页面API
      createApplicationPageAndClass(data).then((res: any) => {
        if (res.code == 0) {
          console.log('页面创建成功:', res);
          const pageId = res.data.id;

          // 根据页面类型初始化数据
          if (pageType.typeNumber === 1 || pageType.typeNumber === 2) {
            // 表单类型（普通表单或流程表单）需要初始化表单数据
            initFormDataDirect(pageId, { name: pageName });
          } else {
            // 其他类型直接跳转
            onSaveSuccess(pageId);
          }
        } else {
          toast.error('创建失败 ' + res.msg);
        }
      });
    };

    // 初始化表单数据（用于直接创建的表单）
    const initFormDataDirect = (pageId: any, info: any) => {
      let data = {
        applicationPageId: pageId,
        data: window.JSON.stringify(createFromObj(info.name, pageId)),
        field: ''
      };
      createFormData(data).then((res: any) => {
        if (res.code == 0) {
          console.log('表单数据初始化成功');
          toast.success('初始化数据完成~');
          // 跳转到编辑器页面
          history.push(
            `/app${match.params.appId}/design/page${pageId}?editCode=${pageId}`
          );
        } else {
          console.error('表单数据初始化失败:', res.msg);
          toast.error('初始化数据失败 ' + res.msg);
          // 即使初始化失败，也跳转到页面列表
          onSaveSuccess(pageId);
        }
      }).catch((error: any) => {
        console.error('表单数据初始化出错:', error);
        toast.error('初始化数据出错');
        // 出错时也跳转到页面列表
        onSaveSuccess(pageId);
      });
    };

    // 取消分类
    const cancelClass = (item: any) => {
      if (item.type != 2) {
        toast.error('当前不是分类');
        return;
      }
      let data = {
        id: item.id,
        type: item.type,
        operateType: 1
      };
      deleteApplicationPageAndClass(data).then((res: any) => {
        if (res.code === 0) {
          setRighPagetMenu(false);
          handleGetApplicationPageAndClassList();
          toast.success('删除成功');
        } else {
          toast.error(res.msg);
        }
      });
    };

    // 删除页面/分类（右键弹出层删除菜单）
    const delClassOrPage = (item: any) => {
      let data = {
        id: item.id,
        type: item.type,
        operateType: 2
      };
      deleteApplicationPageAndClass(data).then((res: any) => {
        if (res.code === 0) {
          setRighPagetMenu(false);
          handleGetApplicationPageAndClassList();
          toast.success('删除成功');
        } else {
          toast.error(res.msg);
        }
      });
    };
    // 重命名页面/分类（双击）
    // 编辑分类名称 - 双击开启编辑分类名称 input
    const openEditTypeName = (e: any, item: any) => {
      // e.stopPropagation();
      if (match.params.playType != 'admin') return;
      if (e.detail === 2) {
        e.stopPropagation();
        setRightMenuItem(item);
        setRenamePageOrClassItem(item);
        setNewItemName(item.name || '');
        setShowRenameDialog(true);
      }
    };

    // 重命名页面/分类（右键弹出层删除菜单）
    const handleReName = () => {
      console.log('开始重命名:', rightMenuItem);
      setRighPagetMenu(false);
      setRenamePageOrClassItem(rightMenuItem);
      setNewItemName(rightMenuItem.name || '');
      setShowRenameDialog(true);
      console.log('重命名弹框打开 - renamePageOrClassItem:', rightMenuItem);
    };

    // 处理弹框重命名确认
    const handleRenameConfirm = () => {
      if (!newItemName.trim()) {
        toast.error('名称不能为空');
        return;
      }

      if (newItemName.trim() === renamePageOrClassItem.name) {
        console.log('名称没有变化，关闭弹框');
        setShowRenameDialog(false);
        setNewItemName('');
        setRenamePageOrClassItem(false);
        return;
      }

      console.log('名称已变化，调用API更新');
      const updatedItem = {
        ...renamePageOrClassItem,
        name: newItemName.trim()
      };

      updateApplicationPageAndClass(updatedItem).then(res => {
        console.log('API响应:', res);
        if (res.code === 0) {
          console.log('重命名成功');
          setShowRenameDialog(false);
          setNewItemName('');
          setRenamePageOrClassItem(false);
          handleGetApplicationPageAndClassList();
          toast.success('重命名成功');
        } else {
          toast.error(res.msg || '重命名失败');
        }
      }).catch(error => {
        console.error('API调用失败:', error);
        toast.error('重命名失败');
      });
    };

    // 处理弹框重命名取消
    const handleRenameCancel = () => {
      setShowRenameDialog(false);
      setNewItemName('');
      setRenamePageOrClassItem(false);
    };





    // 更新页面信息之分类创建成功后，移入对应创建的分类
    const handlePageMoveClass = (parentId: number) => {
      // 选中的分类id
      let data = checkedPageData;
      data.parentId = parentId;
      updateApplicationPageAndClass(data).then(res => {
        if (res.code === 0) {
          handleGetApplicationPageAndClassList();
          toast.success('创建成功');
        } else {
          toast.error(res.msg);
        }
      });
    };

    // 处理拖拽排序
    const handleDragSort = async (sortedData: any) => {
      console.log('🔄 handleDragSort - 接收到的排序数据:', sortedData);
      console.log('🔄 handleDragSort - 当前appMenuList:', appMenuList);

      try {
        // 从排序数据中提取项目列表
        let sortedItems: any[] = [];

        if (sortedData.items) {
          sortedItems = sortedData.items;
        } else if (sortedData.links) {
          sortedItems = sortedData.links;
        } else if (Array.isArray(sortedData)) {
          sortedItems = sortedData;
        } else {
          console.error('🔄 拖拽排序 - 无法识别的数据格式:', sortedData);
          return { code: -1, msg: '数据格式错误' };
        }

        console.log('🔄 拖拽排序 - 提取的项目列表:', sortedItems);
        console.log('🔄 拖拽排序 - 项目列表详细信息:');
        sortedItems.forEach((item, index) => {
          console.log(`  [${index}] ID: ${item.id}, Name: ${item.name || item.label}, ParentId: ${item.parentId}, Type: ${item.type}`);
        });

        // 检查是否包含页面项目
        const pageItems = sortedItems.filter(item => item.type === 1);
        const categoryItems = sortedItems.filter(item => item.type === 2);
        console.log(`🔄 拖拽排序 - 包含 ${pageItems.length} 个页面项目, ${categoryItems.length} 个分类项目`);

        if (pageItems.length === 0) {
          console.log('⚠️ 警告：没有检测到页面项目的拖拽，可能amis nav组件不支持子项目拖拽排序');
        }

        // 获取原始顺序
        const originalItems = (window as any).originalMenuOrder || [];
        console.log('🔄 拖拽排序 - 原始顺序:', originalItems);

        // 等待一小段时间，让DOM更新完成
        await new Promise(resolve => setTimeout(resolve, 100));

        // 分析排序后的数据结构，检测parentId变化
        const parentIdChanges = new Map();

        // 优先检查全局变量中的拖拽信息
        const draggedItemInfo = (window as any).draggedItemInfo;
        if (draggedItemInfo) {
          console.log('🔄 拖拽排序 - 从全局变量获取到拖拽信息:', draggedItemInfo);

          if (draggedItemInfo.type === 'sort') {
            // 处理分类下页面的排序变化
            console.log('🔄 拖拽排序 - 处理分类下页面排序变化');
            const sortChanges = draggedItemInfo.changes || [];

            const sortUpdatePromises = sortChanges.map((change: any) => {
              const updateData = {
                id: change.itemId,
                sort: change.newSort
              };
              console.log(`🔄 拖拽排序 - 更新分类下页面排序: 项目${change.itemId} 在分类${change.parentId}中从位置${change.oldSort}移动到位置${change.newSort}`);
              return updateApplicationPageAndClass(updateData);
            });

            // 清除全局变量
            (window as any).draggedItemInfo = null;

            // 直接执行更新，跳过后续逻辑
            if (sortUpdatePromises.length > 0) {
              console.log('🔄 拖拽排序 - 开始更新分类下页面排序，共', sortUpdatePromises.length, '个项目');
              const results = await Promise.all(sortUpdatePromises);

              const failedResults = results.filter(result => result.code !== 0);
              if (failedResults.length > 0) {
                console.error('🔄 拖拽排序 - 部分更新失败:', failedResults);
                toast.error(`排序更新失败：${failedResults.length} 个项目更新失败`);
                return { code: -1, msg: '部分项目排序更新失败' };
              }

              console.log('🔄 拖拽排序 - 分类下页面排序更新成功');

              // 刷新页面列表
              setTimeout(() => {
                handleGetApplicationPageAndClassList();
              }, 100);

              toast.success('排序更新成功');
              return { code: 0, msg: '排序更新成功' };
            }
          } else {
            // 处理parentId变化（拖拽到分类下）
            parentIdChanges.set(draggedItemInfo.itemId, draggedItemInfo.newParentId);

            // 重要：将被拖拽的项目添加到sortedItems中，确保它会被更新
            const draggedItem = appMenuList.find((item: any) => item.id === draggedItemInfo.itemId);
            if (draggedItem && !sortedItems.find((item: any) => item.id === draggedItemInfo.itemId)) {
              sortedItems.push(draggedItem);
              console.log('🔄 拖拽排序 - 将被拖拽的项目添加到更新列表:', draggedItem);
            }

            // 清除全局变量
            (window as any).draggedItemInfo = null;
          }
        } else {
          // 检查是否有显著的位置变化，如果有，询问用户是否要移动到分类下
          const significantMoves = [];
          for (let i = 0; i < sortedItems.length; i++) {
            const currentItem = sortedItems[i];
            const originalIndex = originalItems.findIndex((item: any) =>
              (item.id || item) === (currentItem.id || currentItem)
            );

            if (originalIndex !== -1) {
              const positionChange = Math.abs(originalIndex - i);
              if (positionChange > 2 && currentItem.type === 1) { // 只处理页面项目的显著移动
                significantMoves.push({
                  item: currentItem,
                  originalIndex,
                  newIndex: i,
                  positionChange
                });
              }
            }
          }

          // 如果检测到页面项目的显著移动，询问用户
          if (significantMoves.length === 1) {
            const movedItem = significantMoves[0].item;

            // 获取所有分类选项
            const categories = appMenuList.filter((item: any) => item.type === 2);

            if (categories.length > 0) {
              // 弹出选择对话框
              const categoryOptions = categories.map((cat: any) => ({
                label: cat.name,
                value: cat.id
              }));

              categoryOptions.unshift({ label: '根级别（不归属任何分类）', value: null });

              // 这里可以使用amis的dialog或者浏览器的confirm
              const selectedCategoryId = await new Promise<any>((resolve) => {
                // 简单的确认对话框
                const categoryNames = categories.map((cat: any) => cat.name).join('、');
                const confirmMessage = `检测到页面"${movedItem.name || movedItem.label}"位置发生了显著变化。\n\n是否要将其移动到某个分类下？\n\n可选分类：${categoryNames}\n\n点击"确定"选择分类，点击"取消"保持原状态。`;

                const userConfirmed = window.confirm(confirmMessage);
                if (userConfirmed) {
                  // 这里可以实现更复杂的选择逻辑
                  // 暂时默认移动到第一个分类
                  resolve(categories[0].id);
                } else {
                  resolve(null);
                }
              });

              if (selectedCategoryId !== null) {
                parentIdChanges.set(movedItem.id, selectedCategoryId);
                console.log(`🔄 拖拽排序 - 用户选择将项目 ${movedItem.id} 移动到分类 ${selectedCategoryId} 下`);
              }
            }
          }
          console.log('🔄 拖拽排序 - 使用回退方案，不进行parentId变化检测');
        }

        console.log('🔄 拖拽排序 - parentId变化映射:', parentIdChanges);

        // 重新构建层级结构的排序逻辑
        const hierarchicalSort = () => {
          // 将扁平化的sortedItems重新组织成层级结构
          const itemsByParent = new Map();

          // 按parentId分组
          sortedItems.forEach((item: any) => {
            const parentId = item.parentId || 0;
            if (!itemsByParent.has(parentId)) {
              itemsByParent.set(parentId, []);
            }
            itemsByParent.get(parentId).push(item);
          });

          console.log('🔄 拖拽排序 - 按父级分组的项目:', itemsByParent);

          // 为每个分组内的项目重新分配sort值
          const updatePromises: Promise<any>[] = [];

          itemsByParent.forEach((items: any[], parentId: number) => {
            console.log(`🔄 拖拽排序 - 处理分组 ${parentId}:`, items);

            items.forEach((item: any, index: number) => {
              const newSort = index + 1;

              // 查找原始项目数据
              const originalItem = appMenuList.find((orig: any) => orig.id === item.id) ||
                                 appMenuList.flatMap((cat: any) => cat.children || [])
                                          .find((orig: any) => orig.id === item.id);

              // 只有当sort值发生变化时才更新
              if (originalItem && originalItem.sort !== newSort) {
                const updateData: any = {
                  id: item.id,
                  sort: newSort
                };

                console.log(`🔄 拖拽排序 - 更新项目 ${item.name || item.label} 在分组 ${parentId} 中的排序: ${originalItem.sort} -> ${newSort}`);
                updatePromises.push(updateApplicationPageAndClass(updateData));
              }
            });
          });

          return updatePromises;
        };

        // 使用层级排序逻辑
        let updatePromises: Promise<any>[] = hierarchicalSort();

        // 找出发生位置变化的项目（用于备用逻辑）
        const changedItems = [];

        for (let i = 0; i < sortedItems.length; i++) {
          const currentItem = sortedItems[i];
          const originalIndex = originalItems.findIndex((item: any) =>
            (item.id || item) === (currentItem.id || currentItem)
          );

          if (originalIndex !== -1 && originalIndex !== i) {
            changedItems.push({
              id: currentItem.id || currentItem,
              oldSort: originalIndex + 1,
              newSort: i + 1,
              item: currentItem
            });
          }
        }

        console.log('🔄 拖拽排序 - 位置变化的项目:', changedItems);
        console.log('🔄 拖拽排序 - 层级排序产生的更新请求数量:', updatePromises.length);

        // 如果有parentId变化，需要单独处理这些项目
        if (parentIdChanges.size > 0) {
          console.log('🔄 拖拽排序 - 检测到parentId变化，优先处理这些项目');

          // 为每个有parentId变化的项目创建更新请求
          const parentIdUpdatePromises = Array.from(parentIdChanges.entries()).map(([itemId, newParentId]) => {
            const updateData: any = {
              id: itemId,
              parentId: newParentId
            };

            // 查找该项目在sortedItems中的位置，设置sort
            const itemIndex = sortedItems.findIndex((item: any) => item.id === itemId);
            if (itemIndex !== -1) {
              updateData.sort = itemIndex + 1;
            } else {
              // 如果在sortedItems中找不到，使用原始位置
              const originalIndex = originalItems.findIndex((item: any) => (item.id || item) === itemId);
              if (originalIndex !== -1) {
                updateData.sort = originalIndex + 1;
              }
            }

            console.log(`🔄 拖拽排序 - 更新项目parentId: ${itemId} -> 分类 ${newParentId}`, updateData);
            return updateApplicationPageAndClass(updateData);
          });

          updatePromises = updatePromises.concat(parentIdUpdatePromises);
        }

        if (changedItems.length === 2) {
          // 只有两个项目位置发生变化，只更新这两个项目
          console.log('🔄 拖拽排序 - 优化：只更新交换的两个项目');
          const sortUpdatePromises = changedItems.map((changedItem: any) => {
            // 如果这个项目已经在parentId更新中处理过，跳过sort更新
            if (parentIdChanges.has(changedItem.id)) {
              console.log(`🔄 拖拽排序 - 项目 ${changedItem.id} 已在parentId更新中处理，跳过sort更新`);
              return Promise.resolve({ code: 0 });
            }

            const updateData: any = {
              id: changedItem.id,
              sort: changedItem.newSort
            };

            console.log(`🔄 拖拽排序 - 更新项目sort:`, updateData);
            return updateApplicationPageAndClass(updateData);
          });

          updatePromises = updatePromises.concat(sortUpdatePromises);
        } else {
          // 复杂变化，批量更新所有项目
          console.log('🔄 拖拽排序 - 复杂变化：批量更新所有项目');
          const sortUpdatePromises = sortedItems.map((item: any, index: number) => {
            // 如果这个项目已经在parentId更新中处理过，跳过sort更新
            if (parentIdChanges.has(item.id)) {
              console.log(`🔄 拖拽排序 - 项目 ${item.id} 已在parentId更新中处理，跳过sort更新`);
              return Promise.resolve({ code: 0 });
            }

            const updateData: any = {
              id: item.id,
              sort: index + 1
            };

            console.log(`🔄 拖拽排序 - 更新项目sort ${item.name || item.label}:`, updateData);
            return updateApplicationPageAndClass(updateData);
          });

          updatePromises = updatePromises.concat(sortUpdatePromises);
        }

        // 并行执行所有更新请求
        console.log('🔄 拖拽排序 - 开始更新，共', updatePromises.length, '个项目');
        const results = await Promise.all(updatePromises);

        // 检查是否有失败的请求
        const failedResults = results.filter(result => result.code !== 0);

        if (failedResults.length > 0) {
          console.error('🔄 拖拽排序 - 部分更新失败:', failedResults);
          toast.error(`排序更新失败：${failedResults.length} 个项目更新失败`);
          return { code: -1, msg: '部分项目排序更新失败' };
        }

        console.log('🔄 拖拽排序 - 所有项目更新成功');

        // 更新原始顺序缓存
        (window as any).originalMenuOrder = [...sortedItems];

        // 刷新页面列表
        setTimeout(() => {
          handleGetApplicationPageAndClassList();
        }, 100);

        toast.success('排序更新成功');
        return { code: 0, msg: '排序更新成功' };

      } catch (error) {
        console.error('🔄 拖拽排序 - 更新过程中出错:', error);
        toast.error('排序更新失败');
        return { code: -1, msg: '排序更新失败' };
      }
    };

    // 禁止页面右键事件-ID为contextmenu的模块
    const preventDefault = () => {
      document.addEventListener('contextmenu', function (e) {
        e.preventDefault();
      });
    };
    // 打开右键弹出层菜单
    const openRighPageMenu = (e: any, item: any) => {
      // 只有在admin模式下才允许右键操作
      if (match.params.playType != 'admin') {
        return;
      }

      if (e.button === 2) {
        e.preventDefault();
        const rect = document.documentElement.getBoundingClientRect();
        const windowHeight = rect.bottom - rect.top;
        const menuRect: any = document
          .getElementById('customContextMenu')
          ?.getBoundingClientRect();
        const menuHeight: any = menuRect?.bottom - menuRect?.top;
        // 计算鼠标右击位置是否超过页面可视高度一半
        const isAboveHalf = e.clientY < windowHeight / 2;

        if (isAboveHalf) {
          // 如果在一半以上，模块最高处为鼠标右击位置
          setRightPageMenuPosition({
            x: e.clientX,
            y: e.clientY
          });
        } else {
          // 如果在一半以下，模块最低部为鼠标右击位置
          setRightPageMenuPosition({
            x: e.clientX,
            y: e.clientY - (menuHeight || 0)
          });
        }
        setRighPagetMenu(true);
        setRightMenuItem(item);
        setRightTypeMenu(item.type);
        console.log('右键菜单设置 - item.type:', item.type, 'item.name:', item.name, 'item:', item);
      }
    };

    // 退出登录
    const LogOut = () => {
      history.push('/login'); // 先跳转
      setTimeout(() => {
        // 延迟清除状态
        store.setAccessToken('');
        store.setTenantId('');
        store.setUserInfo(false);
      }, 100);
    };

    React.useEffect(() => {
      if (rightPageMenu) {
        preventDefault();
        document.addEventListener('click', function (e) {
          var customContextMenu: any =
            document.getElementById('customContextMenu');
          if (customContextMenu && !customContextMenu.contains(e.target)) {
            // 关闭右键弹出层菜单
            setRighPagetMenu(false);
            // 右键编辑页面菜单的数据item
            setRightMenuItem(false);
            // 重命名数据item
            setRenamePageOrClassItem(false);
          }
        });
      }
    }, [rightPageMenu]);

    // 存储事件监听器的引用，以便后续移除
    const contextMenuListeners = React.useRef<Map<Element, (e: MouseEvent) => void>>(new Map());

    // 使用 MutationObserver 监听导航项的DOM变化，并添加右键事件
    React.useEffect(() => {
      if (match.params.playType !== 'admin') return;

      const addContextMenuToNavItems = () => {
        // 首先移除所有现有的事件监听器
        contextMenuListeners.current.forEach((listener, element) => {
          element.removeEventListener('contextmenu', listener);
        });
        contextMenuListeners.current.clear();

        // 查找所有导航项（包括页面和分类）
        const navItems = document.querySelectorAll('.nav-item-page, .nav-item-wrapper, .cxd-Nav-Menu-item, .cxd-Nav-Menu-submenu-title, .cxd-Nav-Menu-submenu > .cxd-Nav-Menu-item-link');

        navItems.forEach((element: any) => {
          // 尝试从不同的地方获取item数据
          let item: any = null;

          // 方法1: 从元素的文本内容推断
          let nameElement = element.querySelector('.nav-item-name-text, .cxd-Nav-Menu-item-label, .cxd-Nav-Menu-item-link-text, .cxd-Nav-Menu-submenu-title-text');
          let itemName = '';

          if (nameElement) {
            itemName = (nameElement.textContent || nameElement.innerText || '').trim();
          } else {
            // 如果找不到特定的文本元素，尝试使用元素本身的文本内容
            itemName = (element.textContent || element.innerText || '').trim();
            // 清理文本，只保留第一行（分类名称）
            itemName = itemName.split('\n')[0].trim();
          }

          if (itemName) {
            // 在菜单列表中查找匹配的项
            const findItemByName = (menuList: any[], name: string): any => {
              for (const menuItem of menuList) {
                if (menuItem.name === name || menuItem.label === name) {
                  return menuItem;
                }
                if (menuItem.children && menuItem.children.length > 0) {
                  const found = findItemByName(menuItem.children, name);
                  if (found) return found;
                }
              }
              return null;
            };

            item = findItemByName(appMenuList, itemName);
          }

          // 方法2: 从父元素的data属性获取
          if (!item) {
            const parentNav = element.closest('[data-key], [data-id]');
            if (parentNav) {
              const key = parentNav.getAttribute('data-key') || parentNav.getAttribute('data-id');
              if (key) {
                const findItemById = (menuList: any[], id: string): any => {
                  for (const menuItem of menuList) {
                    if (menuItem.id?.toString() === id || menuItem.key === id) {
                      return menuItem;
                    }
                    if (menuItem.children && menuItem.children.length > 0) {
                      const found = findItemById(menuItem.children, id);
                      if (found) return found;
                    }
                  }
                  return null;
                };

                item = findItemById(appMenuList, key);
              }
            }
          }

          // 如果找到了item数据，添加右键事件
          if (item) {
            // 创建事件监听器函数
            const contextMenuHandler = (e: MouseEvent) => {
              e.preventDefault();
              console.log('右键点击导航项:', item);
              openRighPageMenu(e, item);
              return false;
            };

            // 添加事件监听器
            element.addEventListener('contextmenu', contextMenuHandler);

            // 存储监听器引用以便后续移除
            contextMenuListeners.current.set(element, contextMenuHandler);

            console.log('为导航项添加右键事件:', item.name, 'ID:', item.id);
          }
        });
      };

      // 初始添加
      setTimeout(addContextMenuToNavItems, 500);

      // 监听DOM变化
      const observer = new MutationObserver((mutations) => {
        let shouldUpdate = false;
        mutations.forEach((mutation) => {
          // 检查是否有节点添加或删除
          if (mutation.type === 'childList' && (mutation.addedNodes.length > 0 || mutation.removedNodes.length > 0)) {
            shouldUpdate = true;
          }
          // 检查是否有属性变化（可能影响导航项）
          if (mutation.type === 'attributes') {
            shouldUpdate = true;
          }
        });

        if (shouldUpdate) {
          console.log('检测到DOM变化，重新绑定右键事件');
          setTimeout(addContextMenuToNavItems, 200);
        }
      });

      // 开始观察侧边栏的变化
      const asideContainer = document.querySelector('.asidePagesClass');
      if (asideContainer) {
        observer.observe(asideContainer, {
          childList: true,
          subtree: true
        });
      }

      return () => {
        observer.disconnect();
      };
    }, [match.params.playType]); // 移除 appMenuList 依赖，避免频繁重新绑定

    // 单独监听 appMenuList 的变化，重新绑定事件
    React.useEffect(() => {
      if (match.params.playType !== 'admin') return;

      console.log('菜单列表更新，重新绑定右键事件');

      // 延迟重新绑定事件
      setTimeout(() => {
        // 首先移除所有现有的事件监听器
        contextMenuListeners.current.forEach((listener, element) => {
          element.removeEventListener('contextmenu', listener);
        });
        contextMenuListeners.current.clear();

        const navItems = document.querySelectorAll('.nav-item-page, .nav-item-wrapper, .cxd-Nav-Menu-item, .cxd-Nav-Menu-submenu-title, .cxd-Nav-Menu-submenu > .cxd-Nav-Menu-item-link');

        navItems.forEach((element: any) => {
          let item: any = null;
          let nameElement = element.querySelector('.nav-item-name-text, .cxd-Nav-Menu-item-label, .cxd-Nav-Menu-item-link-text, .cxd-Nav-Menu-submenu-title-text');
          let itemName = '';

          if (nameElement) {
            itemName = (nameElement.textContent || nameElement.innerText || '').trim();
          } else {
            itemName = (element.textContent || element.innerText || '').trim();
            itemName = itemName.split('\n')[0].trim();
          }

          if (itemName) {
            const findItemByName = (menuList: any[], name: string): any => {
              for (const menuItem of menuList) {
                if (menuItem.name === name || menuItem.label === name) {
                  return menuItem;
                }
                if (menuItem.children && menuItem.children.length > 0) {
                  const found = findItemByName(menuItem.children, name);
                  if (found) return found;
                }
              }
              return null;
            };

            item = findItemByName(appMenuList, itemName);
          }

          if (item) {
            // 创建事件监听器函数
            const contextMenuHandler = (e: MouseEvent) => {
              e.preventDefault();
              console.log('右键点击导航项 (重新绑定):', item);

              // 记录被右键点击的元素
              lastRightClickedElement.current = element;
              console.log('记录右键点击的元素:', element);

              openRighPageMenu(e, item);
              return false;
            };

            // 添加事件监听器
            element.addEventListener('contextmenu', contextMenuHandler);

            // 存储监听器引用以便后续移除
            contextMenuListeners.current.set(element, contextMenuHandler);
          }
        });
      }, 300);
    }, [appMenuList]);

    // 存储最后一次右键点击的元素
    const lastRightClickedElement = React.useRef<Element | null>(null);




    /* methods 方法 end */

    /* created 初始化 */

    /* created 初始化 end */

    const pageTypeList = [
      {
        name: '新建普通表单',
        typeNumber: 1,
        typeName: 'form',
        imgSrc: type_form
      },
      {
        name: '新建流程表单',
        typeNumber: 2,
        typeName: 'process',
        imgSrc: type_process
      },
      {
        name: 'Excel导入',
        typeNumber: 3,
        typeName: 'excelImport',
        imgSrc: type_excelImport
      },
      {
        name: '添加外部链接',
        typeNumber: 4,
        typeName: 'link',
        imgSrc: type_link
      },
      {
        name: '新建自定义页面',
        typeNumber: 5,
        typeName: 'custompage',
        imgSrc: type_custompage
      },

      {
        name: 'amis报表',
        typeNumber: 7,
        typeName: 'dashboard',
        imgSrc: type_dashboard
      },

      // {
      //   name: 'CRUD',
      //   typeNumber: 12,
      //   typeName: 'CRUD',
      //   imgSrc: type_crud
      // },
      {
        name: '数据源导入',
        typeNumber: 13,
        typeName: 'dataSetImport',
        imgSrc: type_dataSetImport
      },
      {
        name: '芋道网盘',
        typeNumber: 14,
        typeName: 'yudaopan',
        imgSrc: type_yudaopan
      },
      {
        name: '门户',
        typeNumber: 17,
        typeName: 'portlet',
        imgSrc: type_portlet
      }
    ];

    // 首先定义页面类型的接口
    interface PageType {
      name: string;
      typeNumber: number;
      typeName: string;
      imgSrc: any;
    }





    /* page 页面构建渲染 */
    // 工作台
    const renderAside = () => {
      return (
        <>
          {/* 其他操作栏 */}
          {/* {match.params.playType == 'admin' && (
            <div className="headActionBar">
              <div
                className="headActionBar-item"
                onClick={() => {
                  setCheckedPageData(false);
                  setIsInitPageType(false);
                  history.push(
                    `/app${match.params.appId}/${match.params.playType}/appSetting/basicSetting`
                  );
                }}
              >
                <i className="fa-solid fa-gear headActionBar-item-icon"></i>
                <div className="headActionBar-item-name">应用设置</div>
              </div>
              <div
                className={
                  match.params.form == 'appPublish'
                    ? 'headActionBar-item item-click'
                    : 'headActionBar-item'
                }
                onClick={() => {
                  setCheckedPageData(false);
                  setIsInitPageType(false);
                  history.push(
                    `/app${match.params.appId}/${match.params.playType}/appPublish`
                  );
                }}
              >
                <i className="fa-solid fa-upload headActionBar-item-icon"></i>
                <div className="headActionBar-item-name">应用发布</div>
              </div>
              <div
                className={
                  match.params.form == 'approval'
                    ? 'headActionBar-item item-click'
                    : 'headActionBar-item'
                }
                onClick={() => {
                  setCheckedPageData(false);
                  setIsInitPageType(false);
                  history.push(
                    `/app${match.params.appId}/${match.params.playType}/approval`
                  );
                }}
              >
                <i className="fa-solid fa-stamp headActionBar-item-icon"></i>
                <div className="headActionBar-item-name">审批</div>
              </div>
              <div className="headActionBar-line"></div>
            </div>
          )} */}
          {/* 页面导航头部功能已合并到导航内容中，移除重复的头部组件 */}

          {/* 缩起状态下的固定展开按钮 */}
          {isCollapsePage && match.params.playType == 'admin' && !isAppTemplate && (
            <div
              className="fixed-expand-btn"
              onClick={() => setIsCollapsePage(false)}
              title="展开导航"
            >
              <svg
                viewBox="0 0 16 16"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path strokeLinecap="round" strokeLinejoin="round" d="m5.497 2.35 5.657 5.657-5.657 5.657"/>
              </svg>
            </div>
          )}

          {/* 页面导航 type 1页面 2分类*/}
          {!isAppTemplate && !isCollapsePage && (
            <div data-playtype={match.params.playType}>
              {console.log('渲染页面导航内容，searchExpanded:', searchExpanded)}
              <AMISRenderer
                schema={{
                type: 'my-nav-page',
                stacked: true,
                className: `pages-nav-content ${isCollapsePage ? 'collapsed' : ''}`,
                // 添加头部配置到导航内容中 - 访问模式下不显示头部
                header: {
                  enable: match.params.playType === 'admin', // 只在admin模式启用头部
                  showTitle: !isCollapsePage && match.params.playType === 'admin',
                  title: '页面',
                  showSearch: !isCollapsePage && match.params.playType === 'admin',
                  showAddNav: !isCollapsePage && match.params.playType === 'admin',
                  showCollapse: match.params.playType === 'admin', // 缩起按钮只在admin模式显示
                  className: 'page-nav-header-bar',
                  collapsed: isCollapsePage
                },
                // 启用导航项操作栏 - 只在admin模式下启用
                enableItemActions: match.params.playType === 'admin',
                // 配置导航项悬浮操作按钮
                itemActions: [
                  {
                    type: 'dropdown-button',
                    icon: 'fa fa-cog',
                    level: 'link',
                    size: 'xs',
                    className: 'nav-item-settings-btn',
                    hideCaret: true,
                    closeOnClick: true,
                    trigger: 'click', // 点击触发（hover在amis中可能不稳定）
                    buttons: [
                      {
                        type: 'button',
                        label: '修改名称',
                        actionType: 'custom',
                        className: 'nav-menu-item',
                        onAction: (e: any, action: any, data: any) => {

                          // 需要从菜单列表中找到正确格式的数据
                          let correctPageData: any = null;

                          // 查找函数
                          const findItemById = (menuList: any[], targetId: number): any => {
                            for (let item of menuList) {
                              if (item.id == targetId) {
                                return item;
                              }
                              if (item.children && item.children.length > 0) {
                                const found = findItemById(item.children, targetId);
                                if (found) return found;
                              }
                            }
                            return null;
                          };

                          // 在应用模板模式下查找
                          if (isAppTemplate && combinedMenuList.length > 0) {
                            correctPageData = findItemById(combinedMenuList, data.id);
                          } else if (appMenuList.length > 0) {
                            correctPageData = findItemById(appMenuList, data.id);
                          }

                          if (correctPageData) {
                            // 查找对应的DOM元素
                            const navItems = document.querySelectorAll('.cxd-Nav-Menu-item');
                            let targetElement = null;

                            navItems.forEach((element: any) => {
                              const nameElement = element.querySelector('.cxd-Nav-Menu-item-label');
                              if (nameElement && nameElement.textContent?.trim() === correctPageData.name) {
                                targetElement = element;
                              }
                            });

                            if (targetElement) {
                              // 设置最后右键点击的元素
                              lastRightClickedElement.current = targetElement;
                            }

                            // 调用重命名功能 - 使用弹框形式
                            setRenamePageOrClassItem(correctPageData);
                            setRightMenuItem(correctPageData);
                            setNewItemName(correctPageData.name || '');
                            setShowRenameDialog(true);
                          } else {
                            toast.error('未找到页面数据，无法重命名');
                          }
                        }
                      },
                      {
                        type: 'button',
                        label: '设置图标',
                        actionType: 'custom',
                        className: 'nav-menu-item',
                        onAction: (e: any, action: any, data: any) => {
                          // 设置当前要编辑图标的页面项
                          setEditIconItem(data);
                          setShowEditIconDialog(true);
                        }
                      },
                      {
                        type: 'button',
                        label: '${type == 2 ? "取消分类" : "访问"}',
                        actionType: 'custom',
                        className: 'nav-menu-item',
                        onAction: (e: any, action: any, data: any) => {
                          if (data && data.id) {
                            // 如果是分类（type == 2），执行取消分类操作
                            if (data.type == 2) {
                              cancelClass(data);
                            } else {
                              // 如果是页面，执行访问操作
                              // 构建目标路由
                              const targetRoute = `/app${match.params.appId}/${match.params.playType}/page${data.id}`;

                              // 从 appMenuList 中查找正确格式的页面数据
                              let correctPageData = null;

                              // 查找函数
                              const findPageById = (menuList: any[], targetId: number): any => {
                                for (let item of menuList) {
                                  if (item.type === 1 && item.id == targetId) {
                                    return item;
                                  }
                                  if (item.children && item.children.length > 0) {
                                    const found = findPageById(item.children, targetId);
                                    if (found) return found;
                                  }
                                }
                                return null;
                              };

                              // 在应用模板模式下查找
                              if (isAppTemplate && combinedMenuList.length > 0) {
                                correctPageData = findPageById(combinedMenuList, data.id);
                              } else if (appMenuList.length > 0) {
                                correctPageData = findPageById(appMenuList, data.id);
                              }

                              if (correctPageData) {
                                // 使用正确格式的页面数据
                                handleCheckedPageData(correctPageData);
                              } else {
                                // 如果没找到，提示用户
                                toast.error('页面数据不存在，无法访问');
                              }
                            }
                          }
                        }
                      },
                      {
                        type: 'button',
                        label: '删除',
                        actionType: 'custom',
                        className: 'nav-menu-item nav-menu-item-danger',
                        confirmText: '删除后，数据不可恢复，确定要删除菜单吗？',
                        confirmTitle: '删除' ,
                        onAction: (e: any, action: any, data: any) => {
                          // 调用删除功能
                          if (data && data.id) {
                            delClassOrPage(data);
                          }
                        }
                      }
                    ]
                  }
                ],
                links: transformMenuListToNavLinks(searchKeyword ? filteredMenuList : appMenuList),
                draggable: match.params.playType === 'admin',
                saveOrderApi: {
                  url: '/admin-api/system/application-page-and-class/update-sort',
                  method: 'put',
                  requestAdaptor: `
                    console.log('🔄🔄🔄 AMIS拖拽排序被触发!');
                    console.log('🔄🔄🔄 API对象:', api);
                    console.log('🔄🔄🔄 API数据:', api.data);
                    console.log('🔄 requestAdaptor - 接收到的API数据:', api);

                    // 获取排序后的数据，尝试多种可能的数据结构
                    let sortedItems = [];

                    if (api.data) {
                      if (Array.isArray(api.data)) {
                        sortedItems = api.data;
                      } else if (api.data.data && Array.isArray(api.data.data)) {
                        sortedItems = api.data.data;
                      } else if (api.data.items && Array.isArray(api.data.items)) {
                        sortedItems = api.data.items;
                      } else if (api.data.links && Array.isArray(api.data.links)) {
                        sortedItems = api.data.links;
                      } else if (api.data.ids && Array.isArray(api.data.ids)) {
                        // 如果只有id数组，需要从原始数据中获取完整信息
                        sortedItems = api.data.ids.map(id => ({ id: id }));
                      } else {
                        // 尝试直接遍历api.data的属性寻找数组
                        for (let key in api.data) {
                          if (Array.isArray(api.data[key]) && api.data[key].length > 0) {
                            sortedItems = api.data[key];
                            break;
                          }
                        }
                      }
                    }

                    console.log('🔄 requestAdaptor - 提取的sortedItems:', sortedItems);

                    // 检查是否是层级结构变化（拖拽到分类下）
                    let hasHierarchyChange = false;
                    let draggedItemInfo = null;

                    // 获取原始菜单数据进行对比
                    const originalMenuData = window.originalMenuOrder || [];

                    // 递归扁平化sortedItems，检查层级变化和排序变化
                    const flattenItems = (items, parentId = null) => {
                      let result = [];
                      items.forEach((item, index) => {
                        // 将当前项目添加到结果中，包含在当前层级的排序位置
                        const itemWithSort = {...item, parentId: parentId, sortInLevel: index + 1};
                        result.push(itemWithSort);

                        if (item.children && item.children.length > 0) {
                          // 检查子项目的parentId是否发生变化
                          item.children.forEach((child, childIndex) => {
                            // 在原始数据中查找这个子项目
                            const originalChild = originalMenuData.find(orig => orig.id === child.id);
                            if (originalChild && originalChild.parentId !== item.id) {
                              hasHierarchyChange = true;
                              draggedItemInfo = {
                                itemId: child.id,
                                newParentId: item.id,
                                oldParentId: originalChild.parentId
                              };
                              console.log('🔄 requestAdaptor - 检测到层级变化:', draggedItemInfo);
                            }

                            // 检查子项目在同一分类下的排序是否发生变化
                            if (originalChild && originalChild.parentId === item.id && originalChild.sort !== (childIndex + 1)) {
                              console.log('🔄 requestAdaptor - 检测到分类下页面排序变化: 项目' + child.id + ' 从位置' + originalChild.sort + ' 移动到位置' + (childIndex + 1));
                              // 存储排序变化信息
                              if (!window.sortChanges) {
                                window.sortChanges = [];
                              }
                              window.sortChanges.push({
                                itemId: child.id,
                                oldSort: originalChild.sort,
                                newSort: childIndex + 1,
                                parentId: item.id
                              });
                            }
                          });
                          // 递归处理子项目
                          result = result.concat(flattenItems(item.children, item.id));
                        }
                      });
                      return result;
                    };

                    const flattenedItems = flattenItems(sortedItems);
                    console.log('🔄 requestAdaptor - 扁平化后的项目:', flattenedItems);

                    // 检查是否有分类下页面的排序变化
                    const sortChanges = window.sortChanges || [];
                    if (sortChanges.length > 0) {
                      console.log('🔄 requestAdaptor - 检测到分类下页面排序变化:', sortChanges);
                      window.draggedItemInfo = {
                        type: 'sort',
                        changes: sortChanges
                      };
                      // 清除排序变化缓存
                      window.sortChanges = [];
                    }

                    // 如果没有检测到层级变化，尝试另一种方法：比较扁平化后的数据
                    if (!hasHierarchyChange && flattenedItems.length > 0 && originalMenuData.length > 0) {
                      flattenedItems.forEach(item => {
                        const originalItem = originalMenuData.find(orig => orig.id === item.id);
                        if (originalItem && originalItem.parentId !== item.parentId) {
                          hasHierarchyChange = true;
                          draggedItemInfo = {
                            itemId: item.id,
                            newParentId: item.parentId,
                            oldParentId: originalItem.parentId
                          };
                          console.log('🔄 requestAdaptor - 通过扁平化数据检测到层级变化:', draggedItemInfo);
                        }
                      });
                    }

                    // 将层级变化信息存储到全局变量中
                    if (hasHierarchyChange && draggedItemInfo) {
                      window.draggedItemInfo = draggedItemInfo;
                      console.log('🔄 requestAdaptor - 存储拖拽信息到全局变量:', draggedItemInfo);
                    }

                    // 获取原始顺序（从全局变量或缓存中获取）
                    const originalItems = window.originalMenuOrder || [];

                    // 如果没有原始顺序缓存，直接使用批量更新
                    if (originalItems.length === 0) {
                      const updateItems = sortedItems.map((item, index) => ({
                        id: item.id || item,
                        sort: index + 1
                      }));

                      // 更新原始顺序缓存
                      window.originalMenuOrder = [...sortedItems];

                      // 异步处理更新
                      setTimeout(() => {
                        if (window.handlePageDragSort) {
                          window.handlePageDragSort(sortedItems);
                        }
                      }, 0);

                      return {
                        ...api,
                        data: {
                          items: updateItems
                        }
                      };
                    }

                    // 找出发生位置变化的项目
                    const changedItems = [];

                    // 比较新旧顺序，找出位置发生变化的项目
                    for (let i = 0; i < sortedItems.length; i++) {
                      const currentItem = sortedItems[i];
                      const currentId = currentItem.id || currentItem;

                      // 在原始列表中查找当前项目的原始位置
                      const originalIndex = originalItems.findIndex(item => {
                        const originalId = item.id || item;
                        // 确保类型一致的比较
                        return String(originalId) === String(currentId);
                      });

                      // 如果位置发生了变化，记录这个项目
                      if (originalIndex !== -1 && originalIndex !== i) {
                        changedItems.push({
                          id: currentId,
                          oldSort: originalIndex + 1,
                          newSort: i + 1,
                          item: currentItem
                        });
                      }
                    }

                    // 如果只有两个项目位置发生变化，说明是简单的交换操作
                    let updateItems = [];
                    if (changedItems.length === 2) {
                      // 只更新这两个交换的项目
                      updateItems = changedItems.map(item => ({
                        id: item.id,
                        sort: item.newSort
                      }));
                    } else {
                      // 如果变化较复杂，仍然使用批量更新
                      updateItems = sortedItems.map((item, index) => ({
                        id: item.id || item,
                        sort: index + 1
                      }));
                    }

                    // 更新原始顺序缓存
                    window.originalMenuOrder = [...sortedItems];

                    // 异步处理更新
                    setTimeout(() => {
                      if (window.handlePageDragSort) {
                        window.handlePageDragSort(sortedItems);
                      }
                    }, 0);

                    // 返回修改后的请求数据
                    return {
                      ...api,
                      data: {
                        items: updateItems
                      }
                    };
                  `,
                  responseAdaptor: `
                    // 总是返回成功，因为实际更新由 handlePageDragSort 处理
                    return {
                      code: 0,
                      msg: '排序更新成功',
                      data: {}
                    };
                  `
                },
                searchable: match.params.playType === 'admin' ? searchExpanded : true, // 访问模式下直接启用搜索
                searchConfig: {
                  placeholder: '搜索页面...',
                  className: 'page-nav-search'
                },
                collapsed: isCollapsePage,
                onSelect: (item: any) => {
                  console.log('🔄 导航选中项目:', item);
                  console.log('🔄 导航选中项目类型:', item.type, '页面类型:', item.pageType);

                  if (item.pageType) {
                    // 选中页面
                    handleCheckedPageData(item);
                  } else {
                    // 选中分类或其他项目
                    console.log('🔄 选中分类或其他项目:', item);
                    setCheckedPageData(item);
                  }
                },
                activeKey: checkedPageData?.id?.toString(),
                itemRender: (item: any) => renderNavItemContent(item),
                onContextMenu: match.params.playType === 'admin' ? (e: any, item: any) => {
                  openRighPageMenu(e, item);
                } : undefined,
                onCreatePage: (pageType: any) => {
                  handleCreatePageFromNav(pageType);
                },
                onAddCategory: (categoryInfo?: any) => handleNewPagesType(1, categoryInfo),
                onSearch: (keyword: string) => {
                  setSearchKeyword(keyword);
                },
                // 添加头部操作回调
                onHeaderAction: (action: string, data?: any) => {
                  switch (action) {
                    case 'search':
                      // 搜索功能处理
                      if (data && typeof data.keyword === 'string') {
                        // 搜索输入变化 - 更新搜索关键词
                        setSearchKeyword(data.keyword);
                      } else if (data && typeof data.expanded === 'boolean') {
                        // 搜索按钮点击 - 切换搜索展开状态
                        setSearchExpanded(data.expanded);
                      }
                      break;
                    case 'addCategory':
                      handleNewPagesType(1);
                      break;
                    case 'addPage':
                      setIsInitPageType(true);
                      // 不要清空checkedPageData，保持选中的分类信息
                      // setCheckedPageData(false);
                      console.log('🔄 新增页面 - 保持选中的分类信息:', checkedPageData);
                      history.push(
                        `/app${match.params.appId}/${match.params.playType}`
                      );
                      break;
                    case 'collapse':
                      setIsCollapsePage(!isCollapsePage);
                      break;
                  }
                }
              }}
              data={{
                // 传递重命名状态到 data 中，让 MyNavPage 组件能够访问
                showRenameDialog,
                renamePageOrClassItem
              }}
            />
            </div>
          )}

          {/* 应用模板菜单 */}
          {isAppTemplate && (
            <div data-playtype={match.params.playType}>
              <AMISRenderer
              schema={{
                type: 'my-nav-page',
                stacked: true,
                className: 'app-template-nav',
                links: transformCombinedMenuToNavLinks(combinedMenuList),
                searchable: false,
                onSelect: (item: any) => {
                  handleTemplateMenuItemClick(item);
                },
                activeKey: getActiveMenuKey(),
                itemRender: (item: any) => renderTemplateNavItemContent(item),
                onCreatePage: (pageType: any) => {
                  handleCreatePageFromNav(pageType);
                },
                onAddCategory: (categoryInfo?: any) => handleNewPagesType(1, categoryInfo)
              }}
              data={{}}
            />
            </div>
          )}
        </>
      );
    };





    // 头部
    const renderHeader = () => {
      // 检查是否为全屏模式（应用设置、应用发布等）
      const isFullScreenMode = Boolean(match.params.form && ['appSetting', 'appPublish', 'pageManager', 'approval'].includes(match.params.form));

      // 中间导航配置
      const middleNavItems = match.params.playType === 'admin' ? [
        {
          key: 'pageManager',
          label: '页面管理',
          active: !['appSetting', 'appPublish', 'approval'].includes(match.params.form || ''),
          // active: match.params.form === 'pageManager' || !match.params.form,
          onClick: () => {
            setCheckedPageData(false);
            setIsInitPageType(true);
            history.push(`/app${match.params.appId}/${match.params.playType}/pageManager`);
          }
        },
        {
          key: 'appSetting',
          label: '应用设置',
          active: match.params.form === 'appSetting',
          onClick: () => {
            setCheckedPageData(false);
            setIsInitPageType(false);
            history.push(`/app${match.params.appId}/${match.params.playType}/appSetting/basicSetting`);
          }
        },
        {
          key: 'appPublish',
          label: '应用发布',
          active: match.params.form === 'appPublish',
          onClick: () => {
            setCheckedPageData(false);
            setIsInitPageType(false);
            history.push(`/app${match.params.appId}/${match.params.playType}/appPublish`);
          }
        },
        {
          key: 'approval',
          label: '审批',
          active: match.params.form === 'approval',
          onClick: () => {
            setCheckedPageData(false);
            setIsInitPageType(false);
            history.push(`/app${match.params.appId}/${match.params.playType}/approval`);
          }
        }
      ] : [];

      return (
        <CommonHeader
          store={store}
          history={history}
          type="app"
          className="applyPageHeader"
          buttonText={match.params.playType == 'admin' ? '应用搭建' : '工作台'}
          buttonIcon={match.params.playType == 'admin' ? dajian : gongzuo}
          onSwitchView={() => {
            history.push(
              match.params.playType == 'admin'
                ? '/appbuild/home'
                : '/platform/workbench'
            );
          }}
          applyData={applyData}
          openApplyNameInput={openApplyNameInput}
          editApplyName={editApplyName}
          match={match}
          onEditApplyName={openEditApplyName}
          onSaveApplyName={saveEditApplyName}
          showMiddleNav={match.params.playType === 'admin'}
          middleNavItems={middleNavItems}
          showPageTools={match.params.form === 'pageManager'}
          onSearchClick={() => {
            // TODO: 实现搜索功能
            toast.info('搜索功能开发中...');
          }}
          onAddCategoryClick={() => handleNewPagesType(1)}
          onAddPageClick={() => {
            setIsInitPageType(true);
            setCheckedPageData(false);
            history.push(`/app${match.params.appId}/${match.params.playType}`);
          }}
          onToggleCollapse={() => setIsCollapsePage(!isCollapsePage)}
          isCollapsed={isCollapsePage}
        />
      );
    };

    /* page 页面构建渲染 end */

    // 检查是否为全屏模式
    const isFullScreenMode = Boolean(match.params.form && ['appSetting', 'appPublish', 'pageManager', 'approval'].includes(match.params.form));

    // 全屏模式渲染
    if (isFullScreenMode) {
      return (
        <div className="full-screen-layout">
          {renderHeader()}
          <div className="full-screen-content">
            <Switch>
              {/* 页面管理 */}
              {match.params.form === 'pageManager' && (
                <PageManager
                  store={store}
                  history={history}
                  match={match}
                  location={location}
                  appMenuList={appMenuList}
                  isCollapsePage={isCollapsePage}
                  setIsCollapsePage={setIsCollapsePage}
                  handleNewPagesType={handleNewPagesType}
                  setIsInitPageType={setIsInitPageType}
                  setCheckedPageData={setCheckedPageData}
                  applyData={applyData}
                  openEditApplyName={openEditApplyName}
                  saveEditApplyName={saveEditApplyName}
                  openApplyNameInput={openApplyNameInput}
                  editApplyName={editApplyName}
                />
              )}

              {/* 应用设置 */}
              {match.params.form === 'appSetting' && !match.params.fullScreen && (
                <div className="full-screen-app-setting">
                  <AppSetting
                    store={store}
                    history={history}
                    match={match}
                    location={location}
                  />
                </div>
              )}

              {/* 应用发布 */}
              {match.params.form === 'appPublish' && (
                <div className="full-screen-app-publish">
                  <AppPublish
                    appId={match.params.appId}
                    store={store}
                    history={history}
                    pageData={checkedPageData}
                  />
                </div>
              )}

              {/* 审批 */}
              {match.params.form === 'approval' && (
                <div className="full-screen-approval">
                  <Approval
                    applyData={applyData}
                    store={store}
                    history={history}
                    match={match}
                    location={location}
                  />
                </div>
              )}

              {/* 全屏设置 */}
              {match.params.form === 'appSetting' &&
                match.params.appSetMenu === 'dataSet' &&
                !!match.params.fullScreen && (
                  <div className="full-screen-data-set">
                    <FullScreen
                      store={store}
                      history={history}
                      match={match}
                      location={location}
                    />
                  </div>
                )}
            </Switch>
          </div>
        </div>
      );
    }

    // 普通布局模式
    return (
      <>
        <Layout
        aside={renderAside()}
        asideClassName={`asidePagesClass ${isCollapsePage ? 'collapsed' : ''}`}
        header={renderHeader()}
        headerClassName={'headerPagesClassAppPage'}
        headerFixed={true}
        folded={store.asideFolded}
        offScreen={store.offScreen}
        data-playtype={match.params.playType}
      >
        {/* isInitPageType：显示创建页面类型；appMenuList.length：是否存在页面或者分类 ;match.params.form:是否存在from：路由地址*/}
        <Switch>
          {/* 创建页面类型的路由 */}
          {(isInitPageType || appMenuList.length == 0) &&
            !match.params.form &&
            match.params.playType == 'admin' && (
              <InitPageType
                onSaveSuccess={onSaveSuccess}
                pageTypeList={pageTypeList}
                appMenuList={appMenuList}
                checkedPageData={checkedPageData}
                history={history}
                computedMatch={match}
              />
            )}

          {/* 页面内容路由 */}
          {match.params.form && checkedPageData && checkedPageData.pageType && (
            <>
              {/* 普通表单 */}
              {checkedPageData.pageType == 1 && (
                <PageContent
                  pageData={checkedPageData}
                  history={history}
                  match={match}
                  store={store}
                  computedMatch={match}
                  updatePage={() =>
                    handleGetApplicationPageAndClassList(
                      undefined,
                      isAppTemplate
                    )
                  }
                />
              )}
              {/* 新建流程表单 */}
              {checkedPageData.pageType == 2 && (
                <PageProcessContent
                  pageData={checkedPageData}
                  history={history}
                  match={match}
                  store={store}
                  computedMatch={match}
                  updatePage={() =>
                    handleGetApplicationPageAndClassList(
                      undefined,
                      isAppTemplate
                    )
                  }
                />
              )}
              {/* Excel导入 */}
              {/* {checkedPageData.pageType == 3 && (
                    <PageReportContent
                      pageData={checkedPageData}
                      history={history}
                      updatePage={() => handleGetApplicationPageAndClassList()}
                    />
                  )} */}
              {/* 外部链接 */}
              {checkedPageData.pageType == 4 && (
                <PagelinkContent
                  pageData={checkedPageData}
                  history={history}
                  match={match}
                  computedMatch={match}
                  updatePage={() =>
                    handleGetApplicationPageAndClassList(
                      undefined,
                      isAppTemplate
                    )
                  }
                />
              )}
              {/* 自定义 */}
              {checkedPageData.pageType == 5 && (
                <PageCustompage
                  pageData={checkedPageData}
                  history={history}
                  match={match}
                  computedMatch={match}
                  updatePage={() =>
                    handleGetApplicationPageAndClassList(
                      undefined,
                      isAppTemplate
                    )
                  }
                />
              )}
              {/* wiki文档 */}
              {/* {match.params.form &&
                  checkedPageData &&
                  checkedPageData.pageType == 6 && (
                    <PageWikiContent
                      pageData={checkedPageData}
                      history={history}
                      updatePage={() => handleGetApplicationPageAndClassList()}
                    />
                  )} */}
              {/* amis报表 */}
              {checkedPageData.pageType == 7 && (
                <PageDashboardContent
                  pageData={checkedPageData}
                  history={history}
                  match={match}
                  computedMatch={match}
                  updatePage={() =>
                    handleGetApplicationPageAndClassList(
                      undefined,
                      isAppTemplate
                    )
                  }
                />
              )}
              {/* 网盘 */}
              {checkedPageData.pageType == 8 && (
                <PageYunWangPanContent
                  pageData={checkedPageData}
                  history={history}
                  store={store}
                  match={match}
                  computedMatch={match}
                  updatePage={() =>
                    handleGetApplicationPageAndClassList(
                      undefined,
                      isAppTemplate
                    )
                  }
                />
              )}
              {/* 笔记 */}
              {checkedPageData.pageType == 9 && (
                <PageNotesContent
                  pageData={checkedPageData}
                  history={history}
                  store={store}
                  match={match}
                  computedMatch={match}
                  updatePage={() =>
                    handleGetApplicationPageAndClassList(
                      undefined,
                      isAppTemplate
                    )
                  }
                />
              )}
              {/* 多维表格 */}
              {/* {match.params.form &&
                  checkedPageData &&
                  checkedPageData.pageType == 10 && (
                    <PageMultiDimensionalTableContent
                      pageData={checkedPageData}
                      history={history}
                      updatePage={() => handleGetApplicationPageAndClassList()}
                    />
                  )} */}
              {/* 白板 */}
              {checkedPageData.pageType == 10 && (
                <PageWhiteboardContent
                  pageData={checkedPageData}
                  history={history}
                  store={store}
                  match={match}
                  computedMatch={match}
                  updatePage={() =>
                    handleGetApplicationPageAndClassList(
                      undefined,
                      isAppTemplate
                    )
                  }
                />
              )}
              {/* CRUD */}
              {/* {match.params.form &&
                  checkedPageData &&
                  checkedPageData.pageType == 12 && (
                    <PageCRUDpage
                      pageData={checkedPageData}
                      history={history}
                      updatePage={() => handleGetApplicationPageAndClassList()}
                    />
                  )} */}
              {/* 数据源导入 */}
              {checkedPageData.pageType == 13 && (
                <PageDataSetImport
                  pageData={checkedPageData}
                  history={history}
                  match={match}
                  computedMatch={match}
                  updatePage={() =>
                    handleGetApplicationPageAndClassList(
                      undefined,
                      isAppTemplate
                    )
                  }
                />
              )}
              {/* 芋道网盘 */}
              {checkedPageData.pageType == 14 && (
                <PageYudaoPanContent
                  pageData={checkedPageData}
                  history={history}
                  store={store}
                  match={match}
                  computedMatch={match}
                  updatePage={() =>
                    handleGetApplicationPageAndClassList(
                      undefined,
                      isAppTemplate
                    )
                  }
                />
              )}

              {/* 数据管理页面 */}
              {checkedPageData.pageType == 15 && (
                <PageDataManage
                  pageData={checkedPageData}
                  store={store}
                  history={history}
                  match={match}
                  computedMatch={match}
                  updatePage={() =>
                    handleGetApplicationPageAndClassList(
                      undefined,
                      isAppTemplate
                    )
                  }
                />
              )}

              {/* 门户 */}
              {checkedPageData.pageType == 17 && (
                <PagePortletContent
                  pageData={checkedPageData}
                  history={history}
                  match={match}
                  computedMatch={match}
                  updatePage={() =>
                    handleGetApplicationPageAndClassList(
                      undefined,
                      isAppTemplate
                    )
                  }
                />
              )}
            </>
          )}

          {/* 系统功能路由 - 不依赖于菜单项 */}
          {match.params.form && (
            <>
              {/* 待处理 */}
              {match.params.form == 'pending' && (
                <div className="applyReportMian">
                  <AMISRenderer
                    schema={createReportObj(
                      `${fixedPlayMenu.pending.path}?applyID=${applyData.id}&userID=${store.userInfo?.id}&tenantId=${store.tenant_id}&authorization=${store.access_token}`
                    )}
                    embedMode={true}
                  />
                </div>
              )}

              {/* 已处理 */}
              {match.params.form == 'transactors' && (
                <div className="applyReportMian">
                  <AMISRenderer
                    schema={createReportObj(
                      `${fixedPlayMenu.transactors.path}?applyID=${applyData.id}&userID=${store.userInfo?.id}&tenantId=${store.tenant_id}&authorization=${store.access_token}`
                    )}
                    embedMode={true}
                  />
                </div>
              )}

              {/* 我创建的 */}
              {match.params.form == 'create' && (
                <div className="applyReportMian">
                  <AMISRenderer
                    schema={createReportObj(
                      `${fixedPlayMenu.create.path}?applyID=${applyData.id}&userID=${store.userInfo?.id}&tenantId=${store.tenant_id}&authorization=${store.access_token}`
                    )}
                    embedMode={true}
                  />
                </div>
              )}

              {/* 抄送我的 */}
              {match.params.form == 'ccusers' && (
                <div className="applyReportMian">
                  <AMISRenderer
                    schema={createReportObj(
                      `${fixedPlayMenu.ccusers.path}?applyID=${applyData.id}&userID=${store.userInfo?.id}&tenantId=${store.tenant_id}&authorization=${store.access_token}`
                    )}
                    embedMode={true}
                  />
                </div>
              )}

              {/* 审批 */}
              {match.params.form == 'approval' && (
                <Approval
                  applyData={applyData}
                  store={store}
                  history={history}
                  match={match}
                  location={location}
                />
              )}

              {/* 应用发布 */}
              {match.params.form === 'appPublish' && (
                <AppPublish
                  appId={match.params.appId}
                  store={store}
                  history={history}
                  pageData={checkedPageData}
                />
              )}

              {/* 应用设置 */}
              {match.params.form == 'appSetting' &&
                !match.params.fullScreen && (
                  <div className="applyReportMian">
                    <AppSetting
                      store={store}
                      history={history}
                      match={match}
                      location={location}
                    />
                  </div>
                )}

              {/* 全屏设置 */}
              {match.params.form == 'appSetting' &&
                match.params.appSetMenu == 'dataSet' &&
                !!match.params.fullScreen && (
                  <div className="applyReportMian">
                    <FullScreen
                      store={store}
                      history={history}
                      match={match}
                      location={location}
                    />
                  </div>
                )}

              {/* 数据源管理 */}
              {match.params.form == 'dataSource' && (
                <div className="applyReportMian">
                  <DataSource
                    history={history}
                    store={store}
                    applyData={applyData}
                  />
                </div>
              )}

              {/* 数据集管理 */}
              {match.params.form == 'dataSet' && (
                <div className="applyReportMian">
                  <DataSet
                    history={history}
                    store={store}
                    applyData={applyData}
                  />
                </div>
              )}

              {/* 控件字典 */}
              {match.params.form == 'controlDictionary' && (
                <div className="applyReportMian">
                  <ControlDictionary
                    history={history}
                    store={store}
                    applyData={applyData}
                  />
                </div>
              )}

              {/* 菜单管理 */}
              {match.params.form == 'menuManager' && (
                <div className="applyReportMian">
                  <MenuManager
                    history={history}
                    store={store}
                    applyData={applyData}
                    // match={match}
                    onMenuRefresh={() => handleGetApplicationPageAndClassList()}
                    onCreatePage={() => {
                      setIsInitPageType(true);
                      setCheckedPageData(false);
                      history.push(
                        `/app${match.params.appId}/${match.params.playType}`
                      );
                    }}
                  />
                </div>
              )}
            </>
          )}
        </Switch>
        {rightPageMenu && match.params.playType == 'admin' && (
          <div
            id="customContextMenu"
            className="customContextMenu"
            style={{
              left: rightPageMenuPosition.x + 'px',
              top: rightPageMenuPosition.y + 'px'
            }}
          >
            {rightTypeMenu == 1 && (
              <>
                <div
                  className="customContextMenu-item"
                  onClick={() => handleReName()}
                >
                  {/* <i className="fa fa-pen customContextMenu-item-icon"></i> */}
                  <div className="customContextMenu-item-name">重命名</div>
                </div>
                <div
                  className="customContextMenu-item"
                  onClick={() => delClassOrPage(rightMenuItem)}
                >
                  {/* <i className="fa fa-trash customContextMenu-item-icon"></i> */}
                  <div className="customContextMenu-item-name">删除页面</div>
                </div>
              </>
            )}
            {rightTypeMenu == 2 && (
              <>
                <div
                  className="customContextMenu-item"
                  onClick={() => handleReName()}
                >
                  {/* <i className="fa fa-pen customContextMenu-item-icon"></i> */}
                  <div className="customContextMenu-item-name">重命名</div>
                </div>
                <div
                  className="customContextMenu-item"
                  onClick={() => cancelClass(rightMenuItem)}
                >
                  {/* <i className="fa fa-trash customContextMenu-item-icon"></i> */}
                  <div className="customContextMenu-item-name">取消分类</div>
                </div>
                <div
                  className="customContextMenu-item"
                  onClick={() => delClassOrPage(rightMenuItem)}
                >
                  {/* <i className="fa fa-trash customContextMenu-item-icon"></i> */}
                  <div className="customContextMenu-item-name">删除分类</div>
                </div>
              </>
            )}
          </div>
        )}
        {/* 退出确认弹窗 */}
        <AMISRenderer
          show={showLogoutDialog}
          onClose={() => setShowLogoutDialog(false)}
          onConfirm={() => {
            setShowLogoutDialog(false);
            LogOut();
          }}
          schema={LogoutConfirmDialog(showLogoutDialog)}
        />

        {/* 重命名弹框 */}
        {showRenameDialog && (
          <AMISRenderer
            show={showRenameDialog}
            onClose={handleRenameCancel}
            schema={{
              type: 'dialog',
              title: '修改名称',
              size: 'm',
              showCloseButton: true,
              closeOnEsc: true,
              closeOnOutside: true,
              body: {
                type: 'form',
                mode: 'horizontal',
                horizontal: {
                  left: 0,
                  right: 12
                },
                body: [
                  {
                    type: 'input-text',
                    name: 'itemName',
                    label: false,
                    placeholder: '请输入名称',
                    value: newItemName,
                    required: true,
                    validations: {
                      isLength: {
                        minimum: 1,
                        maximum: 50,
                        message: '名称长度应在1-50个字符之间'
                      }
                    },
                    onChange: (value: string) => {
                      setNewItemName(value);
                    }
                  }
                ]
              },
              actions: [
                {
                  type: 'button',
                  label: '取消',
                  level: 'default',
                  onAction: handleRenameCancel
                },
                {
                  type: 'button',
                  label: '确定',
                  level: 'primary',
                  onAction: handleRenameConfirm
                }
              ]
            }}
          />
        )}

        {/* 图标编辑对话框 */}
        {showEditIconDialog && (
          <AMISRenderer
            show={showEditIconDialog}
            onClose={() => {
              setShowEditIconDialog(false);
              setEditIconItem(null);
            }}
            onConfirm={handleIconSave}
            schema={EditNoBgEntryIcon({
              logo: (() => {
                // 获取图标数据：优先从 label.tpl 中提取，其次从 icon 字段
                let iconDataToParse = '';

                if (editIconItem?.label && typeof editIconItem.label === 'object' && editIconItem.label.tpl) {
                  // 从 label.tpl 中提取图标 HTML
                  const tplContent = editIconItem.label.tpl;

                  // 提取第一个 span 元素（图标容器）
                  const iconMatch = tplContent.match(/<span class="nav-item-with-icon">([^]*?)<span class="nav-item-text">/);
                  if (iconMatch && iconMatch[1]) {
                    iconDataToParse = iconMatch[1].trim();
                  }
                } else if (editIconItem?.icon) {
                  // 从 icon 字段获取
                  iconDataToParse = editIconItem.icon;
                }

                // 解析图标数据
                const parsedIcon = parseAppIcon(iconDataToParse);
                return {
                  icon: parsedIcon.icon,
                  iconColor: parsedIcon.iconColor,
                  iconBg: parsedIcon.iconBg
                };
              })()
            })}
          />
        )}
      </Layout>
      </>
    );
  })
);
