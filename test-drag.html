<!DOCTYPE html>
<html>
<head>
    <title>拖拽测试</title>
    <style>
        .container {
            display: flex;
            gap: 20px;
            padding: 20px;
        }
        .category {
            width: 200px;
            height: 300px;
            border: 2px solid #ccc;
            padding: 10px;
            background: #f5f5f5;
        }
        .item {
            padding: 10px;
            margin: 5px 0;
            background: white;
            border: 1px solid #ddd;
            cursor: move;
        }
        .item.dragging {
            opacity: 0.5;
        }
        .category.drag-over {
            background: #e8f5e8;
            border-color: #4CAF50;
        }
    </style>
</head>
<body>
    <h1>拖拽测试</h1>
    <div class="container">
        <div class="category" data-type="2" data-id="1">
            <h3>分类1</h3>
            <div class="item" draggable="true" data-type="1" data-id="101">页面1</div>
            <div class="item" draggable="true" data-type="1" data-id="102">页面2</div>
        </div>
        
        <div class="category" data-type="2" data-id="2">
            <h3>分类2</h3>
            <div class="item" draggable="true" data-type="1" data-id="103">页面3</div>
        </div>
        
        <div class="category" data-type="2" data-id="3">
            <h3>分类3（空）</h3>
        </div>
    </div>

    <div id="log"></div>

    <script>
        let draggedItem = null;
        let dropTarget = null;

        // 拖拽开始
        document.addEventListener('dragstart', (e) => {
            draggedItem = e.target;
            e.target.classList.add('dragging');
            console.log('拖拽开始:', e.target.dataset);
        });

        // 拖拽结束
        document.addEventListener('dragend', (e) => {
            e.target.classList.remove('dragging');
            document.querySelectorAll('.category').forEach(cat => {
                cat.classList.remove('drag-over');
            });
        });

        // 拖拽悬停
        document.addEventListener('dragover', (e) => {
            e.preventDefault();
            const category = e.target.closest('.category');
            if (category && category.dataset.type === '2') {
                dropTarget = category;
                document.querySelectorAll('.category').forEach(cat => {
                    cat.classList.remove('drag-over');
                });
                category.classList.add('drag-over');
            }
        });

        // 拖拽放置
        document.addEventListener('drop', (e) => {
            e.preventDefault();
            const category = e.target.closest('.category');
            
            if (category && draggedItem && category.dataset.type === '2') {
                // 移动元素
                category.appendChild(draggedItem);
                
                // 记录日志
                const log = document.getElementById('log');
                log.innerHTML += `<p>项目 ${draggedItem.dataset.id} 移动到分类 ${category.dataset.id}</p>`;
                
                console.log('拖拽完成:', {
                    item: draggedItem.dataset,
                    target: category.dataset
                });
            }
            
            // 清理
            document.querySelectorAll('.category').forEach(cat => {
                cat.classList.remove('drag-over');
            });
        });
    </script>
</body>
</html>
